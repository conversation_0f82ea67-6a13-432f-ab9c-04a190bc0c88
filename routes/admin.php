<?php

use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\AdminOrdersController;
use App\Http\Controllers\Admin\AdminOutreachController;
use App\Http\Controllers\Admin\AdminPaymentController;
use App\Http\Controllers\Admin\AdminSalesController;
use App\Http\Controllers\Admin\AdminUserController;
use App\Http\Controllers\Admin\AdminWalletController;
use App\Http\Controllers\Admin\AdminWebsiteController;
use App\Http\Controllers\Admin\AdminWithdrawalController;
use App\Http\Controllers\Admin\AdminWriterController;
use App\Http\Controllers\MarketplaceAdminController;
use App\Http\Controllers\Outreach\OutreachController;
use App\Http\Controllers\PaymentSettingsController;
use App\Http\Controllers\Writer\WriterController;
// LEGACY
use App\Livewire\Admin\AdminDashboard;
use App\Livewire\Admin\AdminSiteAdd;
use App\Livewire\Admin\AdminSiteEdit;
use App\Livewire\Admin\adminWebsites;
use App\Livewire\Admin\Orders;
use App\Livewire\Admin\SingleOrder;
use Illuminate\Support\Facades\Route;

// Defaults applied in RouteServiceProvider
// - Middleware: 'auth', 'verified', teamAccess
// - Prefix
// - Routes namespaced with admin.

/**********************************************************************************************************************/
// ------------------------------------------- BACKEND TEAM ACCESS ROUTES ------------------------------------------- //
/**********************************************************************************************************************/
Route::get('/', [AdminController::class, 'dashboard'])->name('dashboard');





/**********************************************************************************************************************/
// -------------------------------------- PERMISSION LEVEL: manage-websites  ---------------------------------------- //
/**********************************************************************************************************************/
Route::middleware(['can:manage-websites'])
    ->group(function (): void {

        // CRUD
        Route::resource('websites', AdminWebsiteController::class)
            ->except(['show']);


        // FIX IN V2 (Imran): Merge urls in single call by perameters ‼️
        // url:'publisher-assignment; (search, create, assign/unassign)
        Route::get('websites/search-publishers', [AdminWebsiteController::class, 'searchPublishers'])
            ->name('websites.search-publishers');

        Route::put('websites/{website}/assign-publisher', [AdminWebsiteController::class, 'assignPublisher'])
            ->name('websites.assign-publisher');

        Route::put('websites/{website}/unassign-publisher', [AdminWebsiteController::class, 'unassignPublisher'])
            ->name('websites.unassign-publisher');

        Route::post('/users/create-publisher', [AdminWebsiteController::class, 'createAndAssignPublisher'])
            ->name('users.create-publisher');


        // FIX IN V2 (Imran): Merge urls in single call by perameters ‼️
        // url:'action' (elegibility check, toggle status, generate signed url, search topics)
        Route::post('/website/check-eligibility', [AdminWebsiteController::class, 'checkWebsiteEligibility'])
            ->name('website.checkeligibility');

        Route::put('/websites/{website}/toggle-status', [AdminWebsiteController::class, 'toggleStatus'])
            ->name('websites.toggle-status');

        Route::post('/websites/{website}/generate-signed-url', [AdminWebsiteController::class, 'generateSignedUrl'])
            ->name('websites.generate-signed-url'); // Generate Signed URL For Publishers to update data without login

        Route::get('/topics/search', [AdminWebsiteController::class, 'searchTopics'])
            ->name('topics.search');


        Route::get('/websites/{website}/activity-logs', [AdminWebsiteController::class, 'getActivityLogs'])
            ->name('websites.activity-logs');


        // Outreach
        Route::prefix('outreach')->name('outreach.')->group(function (): void {
            Route::get('assign-website', [OutreachController::class, 'assignWebsite'])->name('assign');
            Route::put('websites/{website}/reject', [AdminWebsiteController::class, 'rejectOutreachStatus'])
                ->name('reject');
        });
    });




/**********************************************************************************************************************/
// --------------------------------- PERMISSION LEVEL: SALES (ORDER-MANAGEMENT)  ------------------------------------ //
/**********************************************************************************************************************/
Route::middleware(['can:manage-orders'])->prefix('orders')->name('orders.')->group(function (): void {


    // Views
    Route::get('/', [AdminOrdersController::class, 'index'])->name('index');
    Route::get('/{order}', [AdminOrdersController::class, 'orderDetails'])->name('details');
    Route::get('/{order}/{item}', [AdminOrdersController::class, 'orderItemDetails'])->name('item');


    // -------------------------------------------------------------------------------
    // FIX IN V2 (Imran): Merge urls in single call by perameters ‼️
    // Define single route for order update and use passed parameters to make an update
    // url:'action'
    Route::post('/item/{item}/update-requirements', [AdminOrdersController::class, 'updateOrderItemRequirements'])
        ->name('item.update-requirements');

    Route::post('/item/{item}/update-content', [AdminOrdersController::class, 'updateOrderItemContent'])
        ->name('item.update-content');

    Route::put('/item/{item}/assign-writer', [AdminOrdersController::class, 'assignWriter'])
        ->name('assign-writer');

    Route::put('/item/{item}/unassign-writer', [AdminOrdersController::class, 'unassignWriter'])
        ->name('unassign-writer');

    Route::get('/assignments/{item}/edit', [AdminOrdersController::class, 'editAssignment'])
        ->name('assignments.edit');


    // Update assignment (e.g. writer assignment or manual changes)
    Route::put('/item/{item}', [AdminOrdersController::class, 'updateAssignment'])->name('assignment.update');

    Route::put('/orders/item/{item}/update-state', [AdminOrdersController::class, 'updateOrderItemState'])
        ->name('item.update-state');


    // misc
    Route::get('/order/invoice/{order}', [AdminOrdersController::class, 'adminInvoice'])->name('invoice');
    Route::get('orders/{orderItemId}/logs', [AdminOrdersController::class, 'getActivityLogsForOrderItem'])
        ->name('logs');
});





/**********************************************************************************************************************/
// ----------------------------------------------- ACCESS LEVEL: WRITER  -------------------------------------------- //
/**********************************************************************************************************************/
Route::middleware(['can:manage-writers'])->prefix('writers')->group(function (): void {

    // Writer specific pages - remove second middleware and group and use menu.json with roles access permission for role based menu display ‼️
    Route::prefix('writer')->name('writer.')->group(function (): void {
        Route::get('/', [WriterController::class, 'dashboard'])->name('dashboard');
        Route::get('/assignments', [WriterController::class, 'assignments'])->name('assignment.index');
        Route::get('/assignments/{assignment}', [WriterController::class, 'showAssignment'])->name('assignment.show');
        Route::post('/assignments/{item}/update-content', [WriterController::class, 'updateContent'])->name('assignment.update-content');
    });
    Route::get('search', [AdminWriterController::class, 'searchWriters'])->name('writers.search');
});





/**********************************************************************************************************************/
// ------------------------------------------ ACCESS LEVEL: ADMIN + SUPER ADMIN  ------------------------------------ //
/**********************************************************************************************************************/
Route::middleware(['can:admin'])
    ->group(function (): void {


        // -------------------------------------------------------------------------------- //
        //                                     USERS MANAGEMENT                             //
        // -------------------------------------------------------------------------------- //
        Route::resource('users', AdminUserController::class);
        Route::post('/users/{user}/restore', [AdminUserController::class, 'restore'])->name('users.restore');
        Route::post('/users/{user}/delete', [AdminUserController::class, 'delete'])->name('users.delete');
        Route::get('/users/{user}/logs', [AdminUserController::class, 'activityLogs'])->middleware('can:view-activity-logs')->name('users.logs');


        // -------------------------------------------------------------------------------- //
        //                                    TEAM MANAGEMENT                               //
        // -------------------------------------------------------------------------------- //
        Route::get('/teams/outreach', [AdminOutreachController::class, 'index'])->name('outreach.stats');
        Route::get('/teams/sales', [AdminSalesController::class, 'stats'])->name('sales.stats');
        Route::get('/teams/assignments', [AdminWriterController::class, 'assignments'])->name('writers.assignments');


        // -------------------------------------------------------------------------------- //
        //                                PAYMENT / WALLET MANAGEMENT                       //
        // -------------------------------------------------------------------------------- //
        Route::get('/payments', [AdminPaymentController::class, 'index'])->name('payments.index');
        Route::get('/payments/{payment}', [AdminPaymentController::class, 'show'])->name('payments.show');

        Route::get('/wallet', [AdminWalletController::class, 'index'])->name('wallet.index');
        Route::get('/wallet/transactions/{transaction}', [AdminWalletController::class, 'show'])
            ->name('wallet.transactions.details');

        Route::get('withdrawals', [AdminWithdrawalController::class, 'index'])->name('withdrawals.index');
        Route::get('withdrawals/{withdrawal}', [AdminWithdrawalController::class, 'show'])->name('withdrawals.show');
        Route::post('withdrawals/{withdrawal}/approve', [AdminWithdrawalController::class, 'approve'])
            ->name('withdrawals.approve');

        Route::get('/settings/payment-settings', [PaymentSettingsController::class, 'index'])
            ->name('payment-settings.index');

        // ------------------------------------------------------------ //
        //                      WALLET CREDIT                          //
        // ------------------------------------------------------------ //
        Route::get('/wallet/credit', [AdminWalletController::class, 'credit'])->name('wallet.credit');
        Route::post('/wallet/credit', [AdminWalletController::class, 'creditWallet'])->name('wallet.credit.store');
    });





/**********************************************************************************************************************/
// ------------------------------------------ ACCESS LEVEL: SUPER ADMIN ONLY  ------------------------------------ //
/**********************************************************************************************************************/
Route::middleware(['can:super-admin'])
    ->group(function (): void {


        // -------------------------------------------------------------------------------- //
        //                        WEBSITES BULK IMPORT FUNCTIONALITY                        //
        // -------------------------------------------------------------------------------- //
        Route::get('/websites/import', [AdminWebsiteController::class, 'bulkImport'])
            ->name('websites.import');

        Route::post('/websites/import', [AdminWebsiteController::class, 'storeBulkImport'])
            ->name('websites.store-import');


        // why two routes for import when it can be handled via controller in single route ‼️🚨
        Route::post('/websites/import-csv', [AdminWebsiteController::class, 'importCsv'])
            ->name('websites.import-csv');


        // later organize it as per use-case ‼️
        Route::get('/settings', [AdminController::class, 'settings'])->name('settings.index');
    });





// ********************************************************************************************************************** //
// --------------------------------------------  LEGACY ADMIN ROUTES  --------------------------------------------------- //
// ********************************************************************************************************************** //
Route::middleware(['role:superadmin,admin'])->prefix('legacy')->group(function (): void {
    Route::get('/admin', AdminDashboard::class, )->name('admin-dashboard');
    Route::get('/admin/orders', Orders::class)->name('admin-orders');
    Route::get('/admin/order/detail/{order}', SingleOrder::class)->name('admin-single-order');
    Route::get('/admin/order/invoice/{order}', [MarketplaceAdminController::class, 'adminInvoice'])->name('admin-invoice');
    Route::get('/admin/websites', adminWebsites::class)->name('admin-websites');
    Route::get('/admin/site/add', AdminSiteAdd::class)->name('site-editor-form');
    Route::get('/admin/site/edit/{website}', AdminSiteEdit::class)->name('admin-website-single');
});
