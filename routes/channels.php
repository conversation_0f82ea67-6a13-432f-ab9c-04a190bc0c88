<?php

use App\Models\MarketplaceSingleOrderItem;
use Illuminate\Support\Facades\Broadcast;

/*
|--------------------------------------------------------------------------
| Broadcast Channels
|--------------------------------------------------------------------------
|
| Here you may register all of the event broadcasting channels that your
| application supports. The given channel authorization callbacks are
| used to check if an authenticated user can listen to the channel.
|
*/

Broadcast::channel('App.Models.User.{id}', fn($user, $id) => (int) $user->id === (int) $id);

// Add authorization for order-item private channel
Broadcast::channel('order-item.{orderItemId}', function ($user, $orderItemId) {
    // Get the order item
    $orderItem = MarketplaceSingleOrderItem::find($orderItemId);

    if (! $orderItem) {
        return false;
    }

    // Allow access if user is either the publisher or the customer
    return $user->id === $orderItem->website->publisher->id ||
        $user->id === $orderItem->order->user_id;
});


Broadcast::channel('logout-session.{sessionId}', fn($user, $sessionId) => true);
