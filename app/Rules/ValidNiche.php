<?php

namespace App\Rules;

use App\Enums\Niche;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class ValidNiche implements ValidationRule
{
    /*********************************************************************
     * VALIDATE
     *********************************************************************
     *
     * Validates that the provided niche value is a valid enum case.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {


        $validNiches = array_column(Niche::cases(), 'value');

        if (! in_array($value, $validNiches, true)) {
            $fail('The selected :attribute is invalid. Valid options are: ' . implode(', ', $validNiches));
        }
    }
}
