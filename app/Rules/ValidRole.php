<?php

namespace App\Rules;

use App\Enums\Role;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class ValidRole implements ValidationRule
{
    /*********************************************************************
     * VALIDATE
     *********************************************************************
     *
     * Validates that the provided role value is a valid enum case.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $validRoles = array_column(Role::cases(), 'value');

        if (! in_array($value, $validRoles, true)) {
            $fail('The selected :attribute is invalid. Valid options are: ' . implode(', ', $validRoles));
        }
    }
}
