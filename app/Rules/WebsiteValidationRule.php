<?php

namespace App\Rules;

use App\Models\MarketplaceWebsite;
use App\Services\AllowedDomains;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Str;

class WebsiteValidationRule implements ValidationRule
{
    protected $email;

    /*********************************************************************
     * CONSTRUCTOR
     *********************************************************************
     *
     * Initializes the rule with the email to validate.
     * @param string $email
     * The email to validate
     *********************************************************************/
    public function __construct($email)
    {
        $this->email = $email;
    }


    /*********************************************************************
     * VALIDATE
     *********************************************************************
     *
     * Validates the website domain based on the following criteria:
     * 1. The domain must be valid and match the email domain
     * 2. The domain must be in a valid format
     * 3. The domain must not be banned
     * 4. The domain must not already be in use
     *
     * @param string $attribute
     * The attribute to validate
     *
     *********************************************************************/
    public function validate($attribute, $value, $fail): void
    {
        // Clean the domain
        $domain = preg_replace('/^((https?:\/\/)?(www\.)?)/', '', Str::lower(trim($value)));
        $domain = strtok($domain, '/');

        // Validate email format
        if (! $this->email || ! Str::contains($this->email, '@')) {
            $fail(__('The domain must be valid and match your email domain.'));
            return;
        }

        // Get email domain
        $emailDomain = Str::lower(Str::after($this->email, '@'));

        // Check if domain matches email domain
        if ($domain !== $emailDomain) {
            $fail(__('The domain must match the domain of your email.'));
            return;
        }

        // Validate domain format
        if (! preg_match('/^[a-z0-9.-]+\.[a-z]{2,}$/', $domain)) {
            $fail(__('The domain must be valid.'));
            return;
        }

        // Check if domain is banned
        if (AllowedDomains::isBanned($domain)) {
            $fail(__('This domain is not allowed.'));
            return;
        }

        // Check if domain already exists
        if (MarketplaceWebsite::where('website_domain', $domain)->exists()) {
            $fail(__('This domain is already in use.'));
            return;
        }
    }
}
