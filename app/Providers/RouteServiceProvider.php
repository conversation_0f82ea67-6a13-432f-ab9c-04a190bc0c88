<?php

namespace App\Providers;

use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Route;

class RouteServiceProvider extends ServiceProvider
{
    /******************************************************************
     * The path to your application's "home" route.
     * Typically, users are redirected here after authentication.
     * @var string
    /*******************************************************************/
    public const HOME = '/marketplace';



    /******************************************************************
     * Define route model bindings, pattern filters, and route config.
    /*****************************************************************/
    public function boot(): void
    {

        /*************************************************
         *  API RATE LIMITING
         *************************************************/
        RateLimiter::for('api', fn(Request $request) => Limit::perMinute(60)->by($request->user()?->id ?: $request->ip()));



        /*************************************************
         * ROUTE FILES
         *************************************************/
        $this->routes(function (): void {



            //------------------------------------------//
            //                    WEB                   //
            //------------------------------------------//
            Route::middleware('web')
                ->group(base_path('routes/web.php'));



            //------------------------------------------//
            //                  PUBLISHER               //
            //------------------------------------------//
            Route::middleware('web')
                ->prefix('publisher')
                ->name('publisher.')
                ->group(base_path('routes/publisher.php'));



            //------------------------------------------//
            //                  ADVERTISER              //
            //------------------------------------------//
            Route::middleware(['web', 'auth', 'verified', 'role:advertiser'])
                ->prefix('advertiser')
                ->name('advertiser.')
                ->group(base_path('routes/advertiser.php'));



            //------------------------------------------//
            //                  ADMIN                   //
            //------------------------------------------//
            Route::middleware(['web', 'auth', 'verified', 'teamAccess'])
                ->prefix(config('pressbear.admin_prefix'))
                ->name('admin.')
                ->group(base_path('routes/admin.php'));



            //------------------------------------------//
            //                   API                    //
            //------------------------------------------//
            Route::middleware('api')
                ->prefix('api')
                ->group(base_path('routes/api.php'));



            // ------------------------------------------
            // Remove and move to relevant route files ⚠️ @hassan we need to organize this better so thats why we have created signed.php which looks organized
            // Add the Signed routes file
            Route::middleware('signed')
                ->group(base_path('routes/signed.php'));
        });
    }
}
