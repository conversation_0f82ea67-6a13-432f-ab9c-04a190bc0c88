<?php

namespace App\Providers;

// use Illuminate\Support\Facades\Gate;
use App\Enums\Role;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;

class AuthServiceProvider extends ServiceProvider
{
    /*******************************************************
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     ********************************************************/
    protected $policies = [
        //
    ];


    /*********************************************************************
     * BOOT METHOD - REGISTER AUTHENTICATION GATES
     *********************************************************************
     *
     * Register role-based and permission-based authorization gates.
     * - Role-based gates: super-admin, admin, sales, finance, outreach, writer, publisher, advertiser
     * - Permission-based gates: manage-users, manage-payments, manage-orders, manage-websites, manage-content, view-analytics, manage-settings
     *
     * Each gate defines access levels and inheritance hierarchy for different user roles.
     *
     * @return void
     *
     *********************************************************************/
    public function boot(): void
    {
        // --------------------------------------------------------------------------------
        // ROLE BASED GATES
        // --------------------------------------------------------------------------------
        $this->registerRoleBasedGates();


        // --------------------------------------------------------------------------------
        // PERMISSION BASED GATES
        // --------------------------------------------------------------------------------
        $this->registerPermissionBasedGates();
    }


    /*********************************************************************
     * REGISTER ROLE BASED GATES - ROLE HIERARCHY DEFINITION
     *********************************************************************
     *
     * Define role-based access control gates with inheritance hierarchy.
     * - SuperAdmin: Highest access level, inherits all permissions
     * - Admin: Administrative functions, inherits most permissions
     * - Sales: Order management and analytics access
     * - Finance: Payment and transaction management
     * - Outreach: Website management and analytics
     * - Writer: Content creation and management (no inheritance)
     * - Publisher: Website and content management (no inheritance)
     * - Advertiser: Campaign management (no inheritance)
     *
     * @return void
     *
     *********************************************************************/
    private function registerRoleBasedGates(): void
    {
        // -----------------------
        // SuperAdmin Gate
        // Highest level access with all system permissions
        Gate::define('super-admin', fn($user) => $user->role === Role::SuperAdmin->value);


        // -----------------------
        // Admin Gate
        // Administrative functions with user, payment, order, and website management
        Gate::define('admin', fn($user) => in_array($user->role, [Role::SuperAdmin->value, Role::Admin->value], true));


        // -----------------------
        // Sales Gate
        // Order management, analytics, and customer information access
        Gate::define('sales', fn($user) => in_array($user->role, [Role::SuperAdmin->value, Role::Admin->value, Role::Sales->value], true));


        // -----------------------
        // Finance Gate
        // Payment management, transaction handling, and financial analytics
        Gate::define('finance', fn($user) => in_array($user->role, [Role::SuperAdmin->value, Role::Admin->value, Role::Finance->value], true));


        // -----------------------
        // Outreach Gate
        // Website management and website analytics access
        Gate::define('outreach', fn($user) => in_array($user->role, [Role::SuperAdmin->value, Role::Admin->value, Role::Outreach->value], true));


        // -----------------------
        // Writer Gate
        // Content creation and assignment management (role-specific, no inheritance)
        Gate::define('writer', fn($user) => $user->role === Role::Writer->value);


        // -----------------------
        // Publisher Gate
        // Website management, content creation, and personal analytics (role-specific, no inheritance)
        Gate::define('publisher', fn($user) => $user->role === Role::Publisher->value);


        // -----------------------
        // Advertiser Gate
        // Campaign management and personal analytics (role-specific, no inheritance)
        Gate::define('advertiser', fn($user) => $user->role === Role::Advertiser->value);
    }


    /*********************************************************************
     * REGISTER PERMISSION BASED GATES - RESOURCE ACCESS CONTROL
     *********************************************************************
     *
     * Define permission-based gates for specific resource access.
     * - manage-users: User account management
     * - manage-payments: Payment and transaction management
     * - manage-orders: Order processing and management
     * - manage-websites: Website administration
     * - manage-content: Content creation and editing
     * - view-analytics: Analytics and reporting access
     * - manage-settings: System configuration management
     *
     * @return void
     *
     *********************************************************************/
    private function registerPermissionBasedGates(): void
    {
        // -----------------------
        // User Management Gate
        // Controls user account administration access
        Gate::define('manage-users', fn($user) => in_array($user->role, [Role::SuperAdmin->value, Role::Admin->value], true));


        // -----------------------
        // Payment Management Gate
        // Controls payment and transaction administration access
        Gate::define('manage-payments', fn($user) => in_array($user->role, [Role::SuperAdmin->value, Role::Admin->value, Role::Finance->value], true));


        // -----------------------
        // Order Management Gate
        // Controls order processing and administration access
        Gate::define('manage-orders', fn($user) => in_array($user->role, [
            Role::SuperAdmin->value,
            Role::Admin->value,
            Role::Sales->value,
            Role::Finance->value,
        ], true));


        // -----------------------
        // Website Management Gate
        // Controls website administration and management access
        Gate::define('manage-websites', fn($user) => in_array($user->role, [
            Role::SuperAdmin->value,
            Role::Admin->value,
            Role::Outreach->value,
        ], true));


        // -----------------------
        // Content Management Gate
        // Controls content creation and editing access
        Gate::define('manage-content', fn($user) => in_array($user->role, [
            Role::SuperAdmin->value,
            Role::Admin->value,
            Role::Writer->value,
            Role::Publisher->value,
        ], true));


        // -----------------------
        // Analytics View Gate
        // Controls analytics and reporting access
        Gate::define('view-analytics', fn($user) => in_array($user->role, [
            Role::SuperAdmin->value,
            Role::Admin->value,
            Role::Sales->value,
            Role::Finance->value,
            Role::Publisher->value,
        ], true));


        // -----------------------
        // Settings Management Gate
        // Controls system configuration and settings access
        Gate::define('manage-settings', fn($user) => in_array($user->role, [Role::SuperAdmin->value, Role::Admin->value], true));

        // -----------------------
        // Writer Search Gate
        // Controls writer search access
        Gate::define('manage-writers', fn($user) => in_array($user->role, [Role::SuperAdmin->value, Role::Admin->value, Role::Writer->value], true));

        // -----------------------
        // Activity Logs View Gate
        // Controls activity logs access - excludes outreach and publisher roles
        Gate::define('view-activity-logs', fn($user) => ! in_array($user->role, [Role::Outreach->value, Role::Publisher->value], true));
    }
}
