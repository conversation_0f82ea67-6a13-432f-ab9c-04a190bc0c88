<?php

namespace App\Services;

use App\Models\MarketplaceAdminWebsite;
use Illuminate\Support\Facades\DB;

class OutreachService
{
    /*********************************************************************
     * FETCH TOP OUTREACH WEBSITE
     *********************************************************************
     *
     * Retrieves the top website (with highest organic traffic) that is
     * unassigned for outreach. It checks for websites without an outreach
     * record, adds organic traffic data from the SEO stats table, orders
     * by organic traffic descending, and returns the first matching website.
     *
     * @return MarketplaceAdminWebsite|null
     * The top unassigned outreach website or null if none found
     *
     *********************************************************************/
    public static function fetchTopOutreachWebsite()
    {

        $outreachWebsites = MarketplaceAdminWebsite::withoutGlobalScopes()
            ->with('outreach')
            ->doesntHave('outreach')
            ->addSelect([
                'organic_traffic' => DB::table('marketplace_website_seo_stats')
                    ->select('ahref_organic_traffic')
                    ->whereColumn('marketplace_website_id', 'marketplace_websites.id')
                    ->limit(1),
            ])
            ->orderByDesc('organic_traffic')
            ->where(function ($query): void {
                $query->where(['publisher_user_id' => 0, 'active' => 0]); //->whereNull('contact_email');
            })
            ->first();

        return $outreachWebsites;
    }






    /*********************************************************************
     * COUNT UNASSIGNED OUTREACH WEBSITES
     *********************************************************************
     *
     * Counts the total number of unassigned outreach websites (no outreach
     * ...record) that meet the criteria: inactive and with no assigned publisher.
     *
     * Organic traffic is also included in the selection but used only
     * ...for ordering, not for the count itself.
     *
     * @return int
     * The count of unassigned outreach websites
     *
     *********************************************************************/
    public static function countUnassignedOutreachWebsites(): int
    {

        $outreachWebsites = MarketplaceAdminWebsite::withoutGlobalScopes()
            ->with('outreach')
            ->doesntHave('outreach')
            ->addSelect([
                'organic_traffic' => DB::table('marketplace_website_seo_stats')
                    ->select('ahref_organic_traffic')
                    ->whereColumn('marketplace_website_id', 'marketplace_websites.id')
                    ->limit(1),
            ])
            ->orderByDesc('organic_traffic')
            ->where(function ($query): void {
                $query->where(['publisher_user_id' => 0, 'active' => 0]); //->whereNull('contact_email');
            })
            ->count();

        return $outreachWebsites;
    }
}
