<?php

namespace App\Services\Domain;

use Pdp\Domain;
use Pdp\Rules;
use Pdp\SyntaxError;

class ExtractDomainNameService
{
    protected Rules $rules;

    public function __construct()
    {
        // Load the PSL (cache periodically)
        $this->rules = Rules::fromPath(storage_path('cache/public-suffix-list.dat'));
    }

    /*********************************************************************
     * EXTRACT DOMAIN NAME
     *********************************************************************
     *
     * Extract the domain name from any URL.
     *
     * @param string $url
     * @return string|null
     *
     *********************************************************************/
    public function extract(string $url): ?string
    {
        // Add http:// if missing
        if (! str_starts_with($url, 'http://') && ! str_starts_with($url, 'https://')) {
            $url = 'https://' . $url;
        }

        $host = parse_url($url, PHP_URL_HOST);

        if (! $host || ! str_contains($host, '.')) {
            return null;
        }

        try {
            $domain = Domain::fromIDNA2008($host);
            $result = $this->rules->resolve($domain);

            // Return the registrable domain
            return $result->registrableDomain()?->toString();
        } catch (SyntaxError $e) {
            // Optionally log here or silently fail
            report($e); // or \Log::warning($e->getMessage());
            return null;
        }
    }
}
