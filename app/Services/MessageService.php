<?php

namespace App\Services;

use App\Events\NewMessageSent;
use App\Models\Chat;
use App\Models\MarketplaceSingleOrderItem;
use App\Notifications\User\UserNotification;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Broadcast;

class MessageService
{
    /*********************************************************************
     * GET ORDER ITEM MESSAGES
     *********************************************************************
     *
     * Retrieves all messages associated with a specific order item.
     * Messages are ordered by creation date in ascending order.
     *
     * @param MarketplaceSingleOrderItem $orderItem
     * The order item to retrieve messages for
     *
     * @return \Illuminate\Database\Eloquent\Collection
     * Collection of messages for the specified order item
     *
     *********************************************************************/
    public function getMessages(MarketplaceSingleOrderItem $orderItem)
    {
        return Chat::where('order_item_id', $orderItem->id)
            ->orderBy('created_at', 'asc')
            ->get();
    }





    /*********************************************************************
     * STORE NEW MESSAGE
     *********************************************************************
     *
     * Creates and stores a new message with optional file attachment.
     * Sends notification to receiver and broadcasts message event.
     *
     * - Handles file attachment storage if provided
     * - Sends notification to message receiver
     * - Broadcasts message to other connected clients
     *
     * @param array $data
     * Message data including text, order_item_id, sender_id, receiver_id
     *
     * @param UploadedFile|null $attachment
     * Optional file attachment for the message
     *
     * @return Message
     * The newly created message instance
     *
     *********************************************************************/
    public function storeMessage(array $data, ?UploadedFile $attachment = null)
    {
        // -----------------------
        // Create Message Instance
        $message = new Chat([
            'text' => $data['text'] ?? null,
            'order_item_id' => $data['order_item_id'],
            'sender_id' => $data['sender_id'],
            'receiver_id' => $data['receiver_id'],
        ]);


        // -----------------------
        // Handle File Attachment
        if ($attachment) {
            $path = $attachment->store('chat-attachments', 'public');
            $message->attachment_path = $path;
        }

        $message->save();


        // -----------------------
        // Send Notification
        $receiver = $message->receiver;
        $sender = $message->sender;
        $receiver->notify(new UserNotification(
            'New Message Received From ' . $sender->name . ' On Order Item #' . $data['order_item_id'] . '',
            $data['order_item_id'],
            [
                'emailSubject' => 'New Message Received',
                'sender_name' => $sender->name,
                'order_item_id' => $data['order_item_id'],
                'is_open_chat' => true,
            ],
        ));


        // -----------------------
        // Broadcast Message Event
        Broadcast::event(new NewMessageSent($message))->toOthers();

        return $message;
    }





    /*********************************************************************
     * MARK MESSAGES AS READ
     *********************************************************************
     *
     * Updates all unread messages for a specific order item to read status.
     *
     * @param int $orderItemId
     * The ID of the order item whose messages should be marked as read
     *
     * @return void
     *
     *********************************************************************/
    public function markMessagesAsRead($orderItemId): void
    {
        Chat::where('order_item_id', $orderItemId)
            ->where('is_read', false)
            ->update(['is_read' => true]);
    }
}
