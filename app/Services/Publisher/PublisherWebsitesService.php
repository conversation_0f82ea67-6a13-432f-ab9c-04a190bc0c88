<?php

namespace App\Services\Publisher;

use App\Models\DomainVerificationCode;
use App\Models\MarketplaceAdminWebsite;
use App\Notifications\DomainVerificationNotification;
use App\Rules\ValidWebsiteEligibility;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Notification;

class PublisherWebsitesService
{
    /*********************************************************************
     * VALIDATE DOMAIN
     *********************************************************************
     *
     * Validates a domain by checking the verification code.
     *
     * @param Request $request
     * @return array
     * An array containing the success status, message, domain, and email
     *
     *********************************************************************/
    public function validateDomain($request)
    {
        // Validate the request
        return $request->validate([
            'domain' => [
                'required_if:mode,domain',
                'string',
                'max:255',
                'unique:marketplace_websites,website_domain',
                'regex:/^(?!http:\/\/|https:\/\/)([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}$/',
                new ValidWebsiteEligibility(),
            ],
            'email' => [
                'required_if:mode,email',
                'email',
                'max:255',
            ],
            'mode' => 'required|string|in:domain,email',
        ]);
    }

    /*********************************************************************
     * SEND VERIFICATION CODE
     *********************************************************************
     *
     * Sends a verification code to the email address associated with the domain.
     *
     * @param string $domain
     * The domain to send the verification code to
     *
     * @param string $email
     * The email address to send the verification code to
     *
     * @return array
     * An array containing the success status, message, domain, and email
     *
     *********************************************************************/
    public function sendVerificationCode(string $domain, string $email): array
    {
        try {

            // Check email should be from the same domain
            if (! filter_var($email, FILTER_VALIDATE_EMAIL) || ! str_contains($email, $domain)) {
                return [
                    'success' => false,
                    'message' => 'Invalid email address. Please enter an email from the same domain.',
                ];
            }

            // Generate verification code
            $verificationCode = DomainVerificationCode::generate($domain, $email);

            // Send email notification
            Notification::route('mail', $email)
                ->notify(new DomainVerificationNotification($domain, $verificationCode->verification_code));

            return [
                'success' => true,
                'message' => 'Verification code sent successfully.',
                'domain' => $domain,
                'email' => $email,
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to send verification code. Please try again.',
            ];
        }
    }

    /*********************************************************************
     * VERIFY DOMAIN
     *********************************************************************
     *
     * Verifies a domain by checking the verification code.
     *
     * @param string $domain
     * @param string $email
     * @param string $verification_code
     *
     * @return array
     * An array containing the success status, message, domain, and email
     *
     *********************************************************************/
    public function verifyDomain(string $domain, string $email, string $verification_code): array
    {
        try {
            // Check if verification code is valid
            $verificationCode = DomainVerificationCode::where('verification_code', $verification_code)
                ->where('email', $email)
                ->first();

            if (! $verificationCode) {
                return [
                    'success' => false,
                    'message' => 'Invalid verification code.',
                ];
            }

            // Check if email is from the same domain
            if (! filter_var($email, FILTER_VALIDATE_EMAIL) || ! str_contains($email, $domain)) {
                return [
                    'success' => false,
                    'message' => 'Invalid email address. Please enter an email from the same domain.',
                ];
            }

            // Check if verification code is expired
            if ($verificationCode->isExpired()) {
                return [
                    'success' => false,
                    'message' => 'Verification code has expired. Please request a new code.',
                ];
            }

            // Mark verification code as verified
            $verificationCode->markAsVerified();

            return [
                'success' => true,
                'message' => 'Domain verified successfully.',
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to verify domain. Please try again.',
            ];
        }
    }

    /*********************************************************************
     * TOGGLE WEBSITE STATUS
     *********************************************************************
     *
     * Toggles the active status of a website for the publisher.
     *
     * @param Request $request
     * @param MarketplaceAdminWebsite $website
     * @return \Illuminate\Http\RedirectResponse
     *
     *********************************************************************/
    public function toggleStatus(Request $request, MarketplaceAdminWebsite $website): void
    {
        // Ensure the website belongs to the authenticated publisher
        if ($website->publisher_user_id !== auth('web')->id()) {
            abort(403, 'Unauthorized action.');
        }

        $website->update([
            'active' => $request->boolean('active'),
        ]);

        // Update outreach status if it exists
        if ($website->outreach) {
            $website->outreach()->update([
                'status' => $request->boolean('active') ? 'onboarded' : 'inprogress',
            ]);
        }

        // Clear the cache
        Cache::flush();
    }
}
