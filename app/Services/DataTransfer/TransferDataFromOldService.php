<?php

namespace App\Services\DataTransfer;

use App\Helpers\BatchJobManager;
use App\Jobs\DataTransfer\{Order, Website};
use App\Models\MarketplaceOrder;
use App\Models\MarketplaceWebsite;

class TransferDataFromOldService
{
    public function __construct(private int $batchSize, private string $queue) {}

    public function transferDataFromOld()
    {
        try {


            // Transfer Websites
            $query = MarketplaceWebsite::on('old_db')->withoutGlobalScopes();
            $result['website'] = (new BatchJobManager(
                $this->batchSize,
                Website::class,
                $query,
                $this->queue,
            ))();


            // Transfer Orders
            $query = MarketplaceOrder::on('old_db')->withoutGlobalScopes();
            $result['orders'] = (new BatchJobManager(
                $this->batchSize,
                Order::class,
                $query,
                $this->queue,
            ))();

            return $result;
        } catch (\Exception $e) {
            throw $e;
        }
    }
}
