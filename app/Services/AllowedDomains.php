<?php

namespace App\Services;

use Illuminate\Support\Str;

class AllowedDomains
{
    /*********************************************************************
     * GET LIST OF BANNED DOMAINS
     *********************************************************************
     *
     * Provides a list of public email domains that are banned from use.
     *
     * @return array
     * An array of banned domain strings
     *
     *********************************************************************/
    public static function bannedDomains(): array
    {
        return [
            'gmail.com',
            'yahoo.com',
            'hotmail.com',
            'outlook.com',
            'msn.com',
            'icloud.com',
            'live.com',
            'aol.com',
            'protonmail.com',
            'mail.com',
            'yandex.com',
            'zoho.com',
            'pm.me',
            'tutanota.com',
            'fastmail.com',
            'gmx.com',
        ];
    }





    /*********************************************************************
     * CHECK IF DOMAIN IS BANNED
     *********************************************************************
     *
     * Verifies if the provided domain is part of the banned domains list.
     *
     * @param string $domain
     * The domain to check
     *
     * @return bool
     * True if the domain is banned, false otherwise
     *
     *********************************************************************/
    public static function isBanned(string $domain): bool
    {
        $normalized = Str::lower(trim($domain));
        return in_array($normalized, self::banned(), true);
    }




    /*********************************************************************
     * ALIAS FOR BANNED DOMAINS LIST
     *********************************************************************
     *
     * Returns the list of banned domains. This method acts as an alias
     * for the bannedDomains() method.
     *
     * @return array
     * An array of banned domain strings
     *
     *********************************************************************/
    public static function banned(): array
    {
        return self::bannedDomains();
    }
}
