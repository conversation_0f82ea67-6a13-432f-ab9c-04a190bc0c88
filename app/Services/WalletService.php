<?php

namespace App\Services;

use App\Models\MarketplaceSingleOrderItem;

/*********************************************************************
 * WALLET SERVICE
 **********************************************************************
 *
 * This service handles wallet-related operations such as transfers
 * between wallets for order payments and other financial transactions.
 *
 *********************************************************************/
class WalletService
{
    /*********************************************************************
     * CREDIT PUBLISHER WALLET
     **********************************************************************
     *
     * Credits the publisher's wallet with payment for a completed order item.
     *
     * @param MarketplaceSingleOrderItem $orderItem
     * The order item for which the publisher should be credited
     *
     * @return void
     *
     *********************************************************************/
    public static function creditPublisherWallet(MarketplaceSingleOrderItem $orderItem): void
    {
        $publisher = $orderItem->website->publisher;
        if (! $publisher) {
            return;
        }
        super_admin_user()->wallet->transfer($publisher->wallet, $orderItem->publisher_payment_paid * 100, [
            'order_id' => $orderItem->order->id,
            'reference' => 'Order Item #' . $orderItem->getKey() . ' Payment Credit To Publisher',
            'type' => 'order_item_publisher_payment',
            'message' => 'Order item payment credited to publisher wallet',
        ]);
    }
}
