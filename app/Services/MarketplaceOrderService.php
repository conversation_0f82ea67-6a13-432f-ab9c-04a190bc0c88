<?php

namespace App\Services;

use App\Enums\MarketplaceOrderStatus;
use App\Enums\OrderItemStates;
use App\Models\MarketplaceOrder;

class MarketplaceOrderService
{
    /*********************************************************************
     * GET ORDER ITEM Status
     *********************************************************************
     *
     * Retrieves the status of a specific order item.
     *
     * @param MarketplaceOrder $order
     * The order to retrieve status for
     *
     * @return string
     * The status of the order
     *
     *********************************************************************/
    public static function getStatus(MarketplaceOrder $order)
    {
        if ($order->orderItems->isEmpty()) {
            return 'Unknown';
        }

        // Late order - all items are late
        $lateOrder = $order->orderItems->every(fn($item) => $item->estimated_publication_date < now()->format('Y-m-d'));
        if ($lateOrder) {
            return MarketplaceOrderStatus::LATE->value;
        }

        // Map of item states to order statuses
        $stateToStatusMap = [
            OrderItemStates::OrderItemCompleted->value => MarketplaceOrderStatus::COMPLETED->value,
            OrderItemStates::PublicationDelivered->value => MarketplaceOrderStatus::DELIVERED->value,
            OrderItemStates::OrderItemCancelled->value => MarketplaceOrderStatus::CANCELLED->value,
            OrderItemStates::RefundedToWallet->value => MarketplaceOrderStatus::REFUNDED->value,
            OrderItemStates::RequirementsPending->value => MarketplaceOrderStatus::PENDING->value,
        ];

        // Get all unique states in the order
        $itemStates = $order->orderItems->pluck('state_name')->unique()->values();

        // If all items have the same state, return the corresponding status
        foreach ($stateToStatusMap as $itemState => $orderStatus) {
            if ($order->orderItems->every(fn($item) => $item->state_name === $itemState)) {
                return $orderStatus;
            }
        }

        // Handle mixed states scenarios
        $hasCompleted = $itemStates->contains(OrderItemStates::OrderItemCompleted->value);
        $hasDelivered = $itemStates->contains(OrderItemStates::PublicationDelivered->value);
        $hasRefunded = $itemStates->contains(OrderItemStates::RefundedToWallet->value);
        $hasCancelled = $itemStates->contains(OrderItemStates::OrderItemCancelled->value);

        // Count items in each state
        $completedCount = $order->orderItems->where('state_name', OrderItemStates::OrderItemCompleted->value)->count();
        $deliveredCount = $order->orderItems->where('state_name', OrderItemStates::PublicationDelivered->value)->count();
        $refundedCount = $order->orderItems->where('state_name', OrderItemStates::RefundedToWallet->value)->count();
        $cancelledCount = $order->orderItems->where('state_name', OrderItemStates::OrderItemCancelled->value)->count();
        $totalItems = $order->orderItems->count();

        // Check if order is completed (all items are either completed, delivered, refunded, or cancelled)
        $completedItemsCount = $completedCount + $deliveredCount + $refundedCount + $cancelledCount;
        $isOrderCompleted = $completedItemsCount === $totalItems;

        // If order is completed but some items weren't delivered, show PARTIALLY_DELIVERED
        if ($isOrderCompleted && $deliveredCount > 0 && $deliveredCount < $totalItems) {
            return MarketplaceOrderStatus::PARTIALLY_DELIVERED->value;
        }

        // Handle other mixed state scenarios
        if ($hasRefunded && $hasCancelled) {
            // If all items are refunded or cancelled, show the majority status
            return $refundedCount > $cancelledCount
                ? MarketplaceOrderStatus::REFUNDED->value
                : MarketplaceOrderStatus::CANCELLED->value;
        }

        if ($hasRefunded) {
            return MarketplaceOrderStatus::REFUNDED->value;
        }

        if ($hasCancelled) {
            return MarketplaceOrderStatus::CANCELLED->value;
        }

        // Default to in progress if no other status matches
        return MarketplaceOrderStatus::IN_PROGRESS->value;
    }

    /*********************************************************************
     * CALCULATE FINAL RECEIVED AMOUNT
     *********************************************************************
     *
     * Calculates the final amount received for an order after accounting
     * for refunds and cancellations. This represents the actual revenue
     * from the order.
     *
     * @param MarketplaceOrder $order
     * The order to calculate the final received amount for
     *
     * @return float
     * The final received amount after refunds and cancellations
     *
     *********************************************************************/
    public static function calculateFinalReceivedAmount(MarketplaceOrder $order): float
    {
        // Start with the original price paid
        $finalAmount = $order->price_paid;

        // Subtract amounts for refunded and cancelled items
        $refundedAndCancelledItems = $order->orderItems->filter(fn($item) => in_array($item->state_name, [
            OrderItemStates::RefundedToWallet->value,
            OrderItemStates::OrderItemCancelled->value,
        ], true));

        // Calculate total amount to subtract
        $amountToSubtract = $refundedAndCancelledItems->sum('price_paid');

        // Final amount is original amount minus refunded/cancelled amounts
        $finalAmount -= $amountToSubtract;

        // Ensure the amount is never negative
        return max(0, $finalAmount);
    }

    /*********************************************************************
     * UPDATE FINAL RECEIVED AMOUNT
     *********************************************************************
     *
     * Updates the final_received_amount field for an order by calculating
     * the current value based on the order items' states.
     *
     * @param MarketplaceOrder $order
     * The order to update
     *
     * @return void
     *
     *********************************************************************/
    public static function updateFinalReceivedAmount(MarketplaceOrder $order): void
    {
        $finalAmount = self::calculateFinalReceivedAmount($order);

        $order->update([
            'final_received_amount' => $finalAmount,
        ]);
    }
}
