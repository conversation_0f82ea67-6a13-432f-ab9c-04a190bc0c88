<?php

namespace App\Services\Admin;

use App\Enums\Role;
use App\Filters\AdminWebsiteFilters;
use App\Models\MarketplaceAdminWebsite;
use Illuminate\Pagination\LengthAwarePaginator;

class AdminWebsiteService
{
    public function index(array $filters)
    {

        // -----------------------
        // Initial Setup
        // -----------------------
        $user = auth('web')->user();

        // -----------------------
        // Outreach Count
        // -----------------------
        $active_outreach_websites_count = MarketplaceAdminWebsite::withoutGlobalScopes()
            ->where('active', 0)
            ->whereDoesntHave('outreach')
            ->whereDoesntHave('publisher')
            ->count();


        // -----------------------
        // Query Building
        // -----------------------
        $website_query = MarketplaceAdminWebsite::with([
            'publisher' => fn($q) => $q->withCount('websites'),
            'outreach',
            'outreach.user:id,name,email',
        ])
            ->withCount('orders');


        $website_query = (new AdminWebsiteFilters($filters))->apply($website_query, $user);


        // -----------------------
        // Outreach Filters
        // Filter by outreach user and status
        // -----------------------
        if ($filters['outreach_user_id'] ?? false) {
            $website_query->whereHas('outreach', function ($query) use ($filters): void {
                $query->where('user_id', $filters['outreach_user_id']);
            });
        }


        if ($filters['outreach_status'] ?? false && $filters['outreach_status'] !== 'all' && $filters['outreach_status'] !== 'unassigned') {
            $website_query->whereHas('outreach', function ($query) use ($filters): void {
                $query->where('status', $filters['outreach_status']);
            });
        }


        if ($filters['outreach_status'] ?? false && $filters['outreach_status'] === 'unassigned') {
            $website_query->whereDoesntHave('outreach');
        }



        // -----------------------
        // Sorting
        // -----------------------
        $allowedSorts = ['id', 'website_domain', 'updated_at', 'active', 'guest_post_price', 'orders_count'];
        $sortField = in_array($filters['sortField'] ?? 'id', $allowedSorts, true)
            ? $filters['sortField'] ?? 'id'
            : 'id';
        $sortOrder = $filters['sortOrder'] ?? 'desc';
        $website_query->orderBy($sortField, $sortOrder);

        // -----------------------
        // Pagination
        // -----------------------
        $perPage = $filters['perPage'] ?? 10; // why?

        /** @var LengthAwarePaginator $results */
        $results = $website_query->paginate($perPage);
        $results->withQueryString();

        return [
            'websites' => $results,
            'user' => $user,
            'is_outreach' => $user->role === Role::Outreach->value,
            'active_outreach_websites_count' => $active_outreach_websites_count,
            'filters' => array_merge(
                [
                    'searchTerm' => $filters['searchTerm'] ?? '',
                    'status' => $filters['status'] ?? '',
                    'verified' => $filters['verified'] ?? '',
                    'sortField' => 'id',
                    'sortOrder' => 'desc',
                    'perPage' => $perPage,
                    'outreach_status' => $user->role === Role::Outreach->value ? 'inprogress' : 'all',
                ],
                $filters,
            ),
        ];
    }
}
