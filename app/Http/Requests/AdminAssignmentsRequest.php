<?php

namespace App\Http\Requests;

use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class AdminAssignmentsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'perPage' => 'nullable|integer|min:1|max:100',
            'assignmentStatus' => 'nullable|string',
            'orderStatus' => 'nullable|string',
            'sortField' => 'nullable|string',
            'sortOrder' => 'nullable|string',
            'searchTerm' => 'nullable|string|min:2|max:100',
        ];
    }
}
