<?php

namespace App\Http\Requests;

use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class AdminWebsiteIndexRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'outreach_status' => 'nullable|in:active,inactive',
            'outreach_user_id' => 'nullable|exists:publishers,id',
            'sortOrder' => 'nullable|string',
            'sortField' => 'nullable|in:id,created_at',
            'searchTerm' => 'nullable|string',
        ];
    }
}
