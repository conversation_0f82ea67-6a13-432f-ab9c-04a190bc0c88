<?php

namespace App\Http\Requests;

use App\Rules\ValidRole;
use Domain\User\DataTransferObjects\UserData;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class AdminUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'name' => 'required|string|max:255|min:3',
            'email' => 'required|email',
            'role' => ['required', 'string', new ValidRole()],
            'phone' => 'nullable|numeric',
            'country_id' => 'nullable|integer|exists:countries_list,id',
            'company' => 'nullable|string|max:100',
            'address' => 'nullable|string|max:300',
            'city' => 'nullable|string|max:100',
            'postal_code' => 'nullable|string|max:100',
        ];

        // Add email uniqueness rule
        if ($this->isMethod('POST')) {
            $rules['email'] .= '|unique:users,email';
            $rules['password'] = 'required|string|min:8';
        } else {
            $rules['email'] .= '|unique:users,email,' . $this->user->id;
            $rules['password'] = 'nullable|string|min:8';
            $rules['email_verified'] = 'nullable|boolean';
        }

        return $rules;
    }

    /*********************************************************************
     * TO DTO - CONVERT REQUEST TO DATA TRANSFER OBJECT
     *********************************************************************
     *
     * Converts the validated request data to a UserData DTO.
     *
     *********************************************************************/
    public function toDto(): UserData
    {
        return UserData::from($this);
    }
}
