<?php

namespace App\Http\Requests;

use App\Rules\ValidWebsiteEligibility;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class AdminWebsiteStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'website_domain' => [
                'required',
                'string',
                'max:255',
                'unique:marketplace_websites,website_domain',
                new ValidWebsiteEligibility(),
            ],
            'site_language_id' => 'required|integer|exists:marketplace_website_languages,id',
            'main_category_id' => 'required|integer|exists:website_categories,id',
            'guest_post_price' => 'required|numeric|min:0',
            'link_insert_price' => 'required|numeric|min:0',
            'casino_post_price' => 'required|numeric|min:0',
            'adult_post_price' => 'required|numeric|min:0',
            'finance_post_price' => 'required|numeric|min:0',
            'dating_post_price' => 'required|numeric|min:0',
            'cbd_post_price' => 'required|numeric|min:0',
            'crypto_post_price' => 'required|numeric|min:0',
            'example_post_url' => 'nullable|url',
            'site_requirements' => 'nullable|string',
            'turn_around_time_in_days' => 'required|integer|min:0',
            'article_validity_in_months' => 'nullable|integer|min:0',
            'indexed_article' => 'required|boolean',
            'link_relation' => 'required|in:dofollow,nofollow,sponsored',
            'sponsorship_label' => 'required|boolean',
            'homepage_visible' => 'required|boolean',
            'contact_email' => 'nullable|email',
            'active' => 'boolean',
            'internal_note' => 'nullable|string',
            'category_global_rank' => 'nullable|integer|min:0',
            'site_title' => 'nullable|string|max:255',
            'site_description' => 'nullable|string|max:500',
            'domain_registration_date' => 'nullable|date',
            'site_icon_image_id' => 'nullable|integer',
            'publisher_user_id' => 'nullable|integer',
            'site_source' => 'nullable|string|max:100',
            'topics' => 'nullable|array',
            'topics.*' => 'string|max:100',
        ];
    }
}
