<?php

namespace App\Http\Requests;

use App\Enums\Role;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class AdminUserIndexRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'keyword' => 'nullable|string|max:255',
            'role' => ['nullable', 'string', 'in:' . implode(',', array_column(Role::cases(), 'value'))],
            'perPage' => 'nullable|integer|min:1|max:100',
            'sortField' => 'nullable|string|in:id,name,email,role',
            'sortDirection' => 'nullable|string|in:asc,desc',
            'trashed' => 'nullable|string|in:all,trashed,active',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'role.in' => 'Please select a valid role.',
            'perPage.min' => 'Per page must be at least 1.',
            'perPage.max' => 'Per page cannot exceed 100.',
            'sortField.in' => 'Invalid sort field.',
            'sortDirection.in' => 'Sort direction must be either ascending or descending.',
            'trashed.in' => 'Invalid trash filter option.',
        ];
    }
}
