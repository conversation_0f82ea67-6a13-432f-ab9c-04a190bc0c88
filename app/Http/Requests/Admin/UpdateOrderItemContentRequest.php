<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class UpdateOrderItemContentRequest extends FormRequest
{
    /*********************************************************************
     * DETERMINE IF USER IS AUTHORIZED
     *********************************************************************/
    public function authorize(): bool
    {
        return true;
    }


    /*********************************************************************
     * GET VALIDATION RULES
     *********************************************************************/
    public function rules(): array
    {
        return [
            'content' => 'required|json',
            'files.*' => 'nullable|array',
        ];
    }


    /*********************************************************************
     * GET VALIDATED CONTENT DATA
     **********************************************************************
     *
     * Decodes JSON content and validates the inner content structure
     * - with proper validation rules for content fields.
     *
     * @return array
     * @throws \Illuminate\Validation\ValidationException
     *********************************************************************/
    public function getContentData(): array
    {
        $content = json_decode($this->input('content'), true);

        $validator = Validator::make($content, [
            'title' => 'required|string|max:255',
            'content_body' => 'required_without:content_url|string',
            'content_url' => 'required_without:content_body|url|max:255',
            'comments' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        return $content;
    }
}
