<?php

namespace App\Http\Controllers\Writer;

use App\Enums\OrderItemStates;
use App\Http\Controllers\Controller;
use App\Http\Resources\MediaResource;
use App\Models\MarketplaceSingleOrderItem;
use App\Models\Media;
use App\States\OrderItem\ContentAdvertiserReview;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

class WriterController extends Controller
{
    public function __construct() {}


    /*********************************************************************
     * WRITER - DASHBOARD
     *
     * Displays writer dashboard statistics based on selected date filters.
     * Filters data using preset ranges (e.g. today, last 7 days) or custom
     * start/end dates. Calculates count of assignments by state:
     * - Assigned
     * - Advertiser Review
     * - Revision Requested
     * - Awaiting Publisher Approval
     * - Completed (not in the above active states)
     *
     * Returns:
     * - Date filter selections
     * - Assignment stats grouped by state
     *********************************************************************/
    public function dashboard(Request $request)
    {
        $user = auth('web')->user();

        $filters = [
            'preset_range' => $request->input('preset_range', 'show_all'),
            'start_date' => $request->input('start_date'),
            'end_date' => $request->input('end_date'),
        ];

        $applyDateFilter = function ($query) use ($filters) {
            if ($filters['preset_range'] && $filters['preset_range'] !== 'custom') {
                return match ($filters['preset_range']) {
                    'today' => $query->whereDate('updated_at', today()),
                    'yesterday' => $query->whereDate('updated_at', today()->subDay()),
                    'last_7_days' => $query->whereBetween('updated_at', [now()->subDays(6)->startOfDay(), now()->endOfDay()]),
                    'last_30_days' => $query->whereBetween('updated_at', [now()->subDays(29)->startOfDay(), now()->endOfDay()]),
                    'last_90_days' => $query->whereBetween('updated_at', [now()->subDays(89)->startOfDay(), now()->endOfDay()]),
                    'last_12_months' => $query->whereBetween('updated_at', [now()->subMonths(12)->startOfDay(), now()->endOfDay()]),
                    default => $query,
                };
            }

            if ($filters['preset_range'] === 'custom') {
                if ($filters['start_date']) {
                    $query->whereDate('updated_at', '>=', $filters['start_date']);
                }
                if ($filters['end_date']) {
                    $query->whereDate('updated_at', '<=', $filters['end_date']);
                }
            }

            return $query;
        };

        $baseQuery = MarketplaceSingleOrderItem::query()
            ->where('is_content_provided_by_customer', false)
            ->whereHas('content', fn($q) => $q->where('writer_id', $user->id));

        $filteredQuery = $applyDateFilter(clone $baseQuery);

        // This aggregates the total and specific states using multiple Eloquent clone queries for clarity and safety.
        $stats = [
            'total' => $filteredQuery->count(),
            'content_assigned' => (clone $filteredQuery)->where('state', OrderItemStates::ContentAssignedToWriter->value)->count(),
            'advertiser_review' => (clone $filteredQuery)->where('state', OrderItemStates::ContentAdvertiserReview->value)->count(),
            'revision_requested_by_advertiser' => (clone $filteredQuery)->where('state', OrderItemStates::ContentRevisionRequestedByAdvertiser->value)->count(),
            'awaiting_publisher_approval' => (clone $filteredQuery)->where('state', OrderItemStates::ContentAwaitingPublisherApproval->value)->count(),
        ];


        $stats = $stats ? (array) $stats : [
            'total' => 0,
            'content_assigned' => 0,
            'advertiser_review' => 0,
            'revision_requested' => 0,
            'awaiting_approval' => 0,
        ];



        // Completed = not in active states
        $activeStates = [
            OrderItemStates::ContentAssignedToWriter->value,
            OrderItemStates::ContentAdvertiserReview->value,
            OrderItemStates::ContentRevisionRequestedByAdvertiser->value,
            OrderItemStates::ContentAwaitingPublisherApproval->value,
        ];



        $stats['completed'] = $applyDateFilter(clone $baseQuery)
            ->whereNotIn('state', $activeStates)
            ->count();

        return Inertia::render('Writer/Dashboard/Index', [
            'filters' => $filters,
            'stats' => $stats,
        ]);
    }






    /*********************************************************************
     * WRITER - ASSIGNMENTS
     *
     * Displays a paginated list of assignments assigned to the logged-in writer.
     * Filters assignments that:
     * - Are not customer-provided (team content only)
     * - Are in active content-related states (pending, assigned, review, etc.)
     * - Belong to the current writer (`content.writer_id`)
     *
     * Returns:
     * - Paginated assignment list
     * - Status list with label/value pairs for frontend use
     *********************************************************************/

    public function assignments(Request $request)
    {
        $user = auth('web')->user();

        // Default assignmentType to 'assigned'
        $request->merge([
            'assignmentType' => $request->input('assignmentType', 'assigned'),
        ]);

        // Only these are considered 'assigned'
        $assignedStates = [
            OrderItemStates::ContentAssignedToWriter->value,
            OrderItemStates::ContentAdvertiserReview->value,
            OrderItemStates::ContentRevisionRequestedByAdvertiser->value,
            OrderItemStates::ContentAwaitingPublisherApproval->value,
        ];

        // Base query: only assignments belonging to this writer
        $query = MarketplaceSingleOrderItem::with(['website', 'content', 'requirements'])
            ->where('is_content_provided_by_customer', false)
            ->whereHas('content', fn($q) => $q->where('writer_id', $user->id));

        // Filter by type: assigned or completed
        if ($request->assignmentType === 'completed') {
            $query->whereNotIn('state', $assignedStates);
        } elseif ($request->assignmentType === 'assigned') {
            $query->whereIn('state', $assignedStates);
        }


        // Search (by website or requirements)
        if ($request->filled('search')) {
            $query->where(function ($q) use ($request): void {
                $q->whereHas(
                    'website',
                    fn($sub) =>
                    $sub->where('website_domain', 'like', '%' . $request->search . '%'),
                )->orWhereHas(
                    'requirements',
                    fn($sub) =>
                    $sub->where('article_topic', 'like', '%' . $request->search . '%')
                        ->orWhere('anchor_text', 'like', '%' . $request->search . '%')
                        ->orWhere('advertiser_url', 'like', '%' . $request->search . '%'),
                );
            });
        }

        // $query->getCollection()->each(function ($item) {
        //     $item->append('content_status');
        // });

        // Paginate with query string
        $assignments = $query->orderByDesc('id')
            ->paginate(10)
            ->withQueryString();

        $assignments->getCollection()->each->append('content_status');
        // // Append computed attribute to each item in the collection
        // $assignments->getCollection()->each(function ($item) {
        //     $item->append('content_status');
        // });

        return Inertia::render('Writer/Assignments/Index', [
            'assignments' => $assignments,
            'statuses' => collect($assignedStates)->map(fn($state) => [
                'value' => $state,
                'label' => ucwords(str_replace('-', ' ', $state)),
            ]),
            'query' => $request->only('search', 'assignmentType'),
        ]);
    }





    /*********************************************************************
     * WRITER - SHOW ASSIGNMENT DETAILS
     *
     * Displays the details of a single assignment to the writer.
     * Loads the following relationships:
     * - content + media files
     * - website details
     * - requirements for writing
     *
     * Media files are mapped into simplified arrays for Vue component.
     *
     * @param MarketplaceSingleOrderItem $assignment
     * The assignment item to show
     *
     * @return \Inertia\Response
     * Renders the detailed assignment view
     *********************************************************************/

    public function showAssignment(MarketplaceSingleOrderItem $assignment)
    {
        $assignment->load([
            'content.media',
            'website',
            'requirements',
            'order.user.advertiserGuidelines',
        ]);

        $assignment->append('content_status');

        $media = MediaResource::collection(optional($assignment->content)->media ?? []);

        return Inertia::render('Writer/Assignments/Show', [
            'item' => $assignment,
            'media' => $media,
        ]);
    }


    /*********************************************************************
     * WRITER - UPDATE ASSIGNMENT CONTENT
     *
     * Allows a writer to update the content for an assigned order item.
     *
     * Steps:
     * - Validates incoming JSON content data
     * - Validates content structure (title, body or URL, comments)
     * - Updates the associated Content model
     * - Updates associated media file records to link them with content
     * - Transitions the order item state to "ContentAdvertiserReview"
     *
     * @param MarketplaceSingleOrderItem $item
     * The order item being updated
     *
     * @param Request $request
     * The HTTP request containing content and file data
     *
     * @return \Illuminate\Http\RedirectResponse
     * Redirects back with a success message
     *********************************************************************/

    public function updateContent(MarketplaceSingleOrderItem $item, Request $request)
    {


        $user = Auth::user();

        // Check writer ownership
        // if (!$item->content || $item->content->writer_id !== $user->id) {
        //     abort(403, 'Unauthorized');
        // }

        $request->validate([
            'content' => 'required|json',
            'files.*' => 'nullable|array',
        ]);

        $content = json_decode($request->content, true);


        Validator::make($content, [
            'title' => 'required|string|max:255',
            'content_body' => 'required_without:content_url|string',
            'content_url' => 'required_without:content_body|url|max:255',
            'comments' => 'nullable|string',
        ])->validate();

        $mediaIds = $content['files_array'] ?? [];

        $item->content->update([
            ...$content,
            'files_array' => $mediaIds,
        ]);

        // Optional: transition state
        $item->state->transitionTo(ContentAdvertiserReview::class);
        $item->save();

        $item->content->attachMedia($mediaIds);

        return back()->with('success', 'Content updated.');
    }
}
