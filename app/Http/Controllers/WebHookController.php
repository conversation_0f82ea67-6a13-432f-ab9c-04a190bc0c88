<?php

namespace App\Http\Controllers;

use Domain\Payment\Payment;
use Illuminate\Http\Request;

class <PERSON>Hook<PERSON><PERSON>roller extends Controller
{
    /*********************************************************************
     * STRIPE WEBHOOK HANDLER
     *********************************************************************
     *
     * Processes incoming Stripe webhook events and handles payment-related
     * ..notifications.
     *
     * - Validates webhook signature and payload
     * - Processes payment success and failure events
     * - Dispatches payment processing jobs
     *
     * @param Request $request
     * HTTP request containing Stripe webhook payload and signature
     *
     * @return \Illuminate\Http\JsonResponse
     * Returns success response or error with appropriate status code
     *
     *********************************************************************/
    public function stripe(Request $request)
    {
        return (new Payment())->processOrder($request);
    }
}
