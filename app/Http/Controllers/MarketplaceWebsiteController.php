<?php

namespace App\Http\Controllers;

use App\Enums\Niche;
use App\Http\Controllers\FilterQueries\FiltersTrait;
use App\Models\MarketplaceWebsite;
use Domain\Cart\Cart as CartService;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;

class MarketplaceWebsiteController extends Controller
{
    use FiltersTrait;

    public function __construct(
        private CartService $cartService,
    ) {}

    /*************************************************************
     * MARKETPLACE APP MAIN PAGE SHOW
     *************************************************************
     *
     * Return websites + cart data
     *
     * Return Table Fragment Only if fragment
     * is true
     *
     * @param $request request
     * @return view
     *
    /*************************************************************/
    public function marketHome(Request $request, $fragment = false)
    {
        // Home page is cached for 24 hours + Reset button
        if ($request->isMethod('get') || (isset($request->data) && $request->data === 'homepage')) {

            $websites = Cache::remember('websitesListHomePaged', (60 * 60 * 24 * 7), fn() => $this->findWebsites($request, $homepage = true));
        } else {
            // query market websites
            $websites = $this->findWebsites($request);
        }

        // User Cart Data
        $cartData = $this->cartService->getUserCart();
        $cartWebsites = $cartData['items'];


        // return arrat of website_ids for cart
        $cartWebsitesIds = Arr::pluck($cartWebsites, 'website_id');


        // categories/countries/lang lists for ui
        $filtersData = $this->filtersData();

        // If $fragment is true, only the marketTable fragment is returned.
        // If $fragment is false, the full page is returned as usual.

        return view('marketplace.market-app-main', [
            'websites' => $websites,
            'cartItems' => $cartWebsites,
            'cartWebsitesids' => $cartWebsitesIds,
            'cartData' => $cartData,
            'filtersData' => $filtersData,
        ])
            ->fragmentIf($fragment, 'marketTable');
    }





    /*****************************************
     * MARKET WEBSITES DATA GET + FILTER
     *****************************************
     *
     * - GET WEBSITES (default sort)(cached)
     * - FILTER
     * - SEARCH
     * - SORT
     *
     * > Using Joins to filter
     * > Using Fulltext mysql for search
     *
     * @param $request Request
     * @param $homepage bool (default sort)
     * @return $websites Model collection
     *
    /*****************************************/
    public function findWebsites(Request $request, $homepage = false)
    {

        // Query: join four tables for search and quering
        $websites = MarketplaceWebsite::query();
        $websites = $websites->select(
            'marketplace_websites.id as website_id',
            'marketplace_websites.*',
            'marketplace_website_seo_stats.*',
            'marketplace_website_languages.name as languageName',
            'countries_list.name as topTrafficCountryName',
            'countries_list.code as topTrafficCountryCode',
            'website_categories.category',
        )

            ->join(
                'marketplace_website_seo_stats',
                'marketplace_website_seo_stats.marketplace_website_id',
                '=',
                'marketplace_websites.id',
            )

            ->join(
                'marketplace_website_languages',
                'marketplace_websites.site_language_id',
                '=',
                'marketplace_website_languages.id',
            )

            ->join(
                'countries_list',
                'marketplace_website_seo_stats.top_traffic_country_id',
                '=',
                'countries_list.id',
            )

            ->join(
                'website_categories',
                'marketplace_websites.main_category_id',
                '=',
                'website_categories.id',
            );

        // ***********************************/
        // Active Filters
        $filterActive = $this->getActiveFilters($request);
        $activeNiche = 'guest_post_price'; //default if not selected


        // ***********************************/
        // SEARCHING
        if ($filterActive['search']) {
            $searchText = $this->validateReturnSearchText($request);
            $websites = $this->applySearchQuery($websites, $searchText);
        }

        // ***********************************/
        // Niche Selection
        if ($filterActive['niche']) {
            $requestedNiche = $this->validateReturnNicheType($request);        //validate
            $activeNiche = $this->nicheColumnName($requestedNiche);         //column name
            $websites = $this->applyNicheQuery($websites, $activeNiche); //apply if applicavle
        }

        // ***********************************/
        // APPLY FILTERS
        if ($filterActive['filtersApplied']) {

            // ------------
            // Price filter
            if ($filterActive['price']) {

                $price = $this->validateReturnPrice($request);

                $requestedNiche = $request['niche.type'];

                $nichePrice = MarketplaceWebsite::nicheProfitMarginMultiplier($requestedNiche);

                // negatively adjust multiplied prices to get acurate filteration (avoid attributes)
                $price['min'] = round($price['min'] / $nichePrice);
                $price['max'] = round($price['max'] / $nichePrice);

                $websites = $websites->whereBetween($activeNiche, [$price['min'], $price['max']]);
            }

            // ------------
            // DR filter
            if ($filterActive['dr']) {
                $dr = $this->validateReturnDR($request);
                $websites = $websites->whereBetween('ahref_domain_rank', [$dr['min'], $dr['max']]);
            }

            // --------------
            // Traffic Filter
            if ($filterActive['traffic']) {
                $traffic = $this->validateReturnTraffic($request);
                $websites = $websites->whereBetween('ahref_organic_traffic', [$traffic['min'], $traffic['max']]);
            }

            // -----------------
            // Spam Score Filter
            if ($filterActive['spamScore']) {
                $spamScore = $this->validateReturnSpamscore($request);
                $websites = $websites->whereBetween('moz_spam_score', [$spamScore['min'], $spamScore['max']]);
            }

            // ---------------
            // Country Filter
            if ($filterActive['country']) {
                $countriesList = $this->validateReturnCountryList($request);
                $websites = $websites->whereIn('top_traffic_country_id', $countriesList);
            }

            // ---------------
            // Language Filter
            if ($filterActive['language']) {
                $languageList = $this->validateReturnLanguageList($request);
                $websites = $websites->whereIn('site_language_id', $languageList);
            }

            // ---------------
            // Category Filter
            if ($filterActive['category']) {
                $categoryList = $this->validateReturnCategoryList($request);
                $websites = $websites->whereIn('main_category_id', $categoryList);
            }
        }

        // ***********************************/
        // SORTING
        if ($filterActive['sort']) {
            $sort = $this->validateReturnSorting($request);
            $websites = $this->applySortQuery($websites, $sort, $activeNiche);
        }

        // ***********************************/
        // Homepage sorting for home default view (pref)
        if ($homepage) {
            $websites = $this->applyHomeQuery($websites);
        }

        // ***********************************/
        // Active Websites Only + Price Limit
        $websites = $websites->where('active', true);
        $websites = $websites->where($activeNiche, '<', config('app.niche_price_limit', 1000)); //price limit

        // ***********************************/
        // LAZYLOAD: Topics + keywords
        $websites = $websites->with('topics', 'keyword_website');

        // ***********************************/
        // Paginations
        $websites = $websites->paginate(20);

        // ***********************************/
        // correct ids for frontend use cases
        // ids are messed when using joins
        foreach ($websites as $website) {
            $website->id = $website->website_id;
        }

        return $websites;
    }





    /*****************************************
     * FRAGMENTED PARTS
     *****************************************
     *
     * Updates cart preview (heading) when
     * adding and removing items from cart.
     * Also update numbers in marketplace
     * home. (total price + items)
     *
     * @param $request Request
     * @return cart preiew view
     *
    /*****************************************/
    public function marketFragments(Request $request)
    {
        $cartData = $this->cartService->getUserCart();
        $cartWebsites = $cartData['items'];

        // JSON data for updating other data
        $data['totalPrice'] = $cartData['stats']['totalPrice'];
        $data['itemsCount'] = $cartData['stats']['count'];

        //Render HTML for return in json api
        $cartHTML = view('fragments.cart-preview')->with('cartItems', $cartWebsites)->render();

        if ($request->cartPreview) {
            return ['cartHTML' => $cartHTML, 'data' => $data];
        }
        return true;

    }





    /*****************************************
     * CACHED DATA: FOR FILTERS IN UI
    /*****************************************/
    public function filtersData()
    {
        $filtersData['countries'] = MarketplaceMetaDataController::countriesList();
        $filtersData['languages'] = MarketplaceMetaDataController::LanguagesList();
        $filtersData['categories'] = MarketplaceMetaDataController::categoriesList();

        return $filtersData;
    }





    /*****************************************
     *
     * MARKET TABLE: FRAGMENT (legacy)
     *
     * Return HTML For Table
     * Process:
     * - Filteration
     * - Search
     * - Niche Selection
     *
    /*****************************************/
    public function marketTable(Request $request)
    {
        return $this->marketHome($request, true);
    }
}
