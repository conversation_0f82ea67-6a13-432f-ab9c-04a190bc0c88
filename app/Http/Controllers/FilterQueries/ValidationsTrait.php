<?php

namespace App\Http\Controllers\FilterQueries;

use Illuminate\Http\Request;

trait ValidationsTrait
{
    /*********************************************************************
     * SEARCH TERM VALIDATION
     *********************************************************************
     *
     * Validates and returns the search term from the request.
     * Ensures the search term meets length and character requirements.
     *
     * @param Request $request
     * The HTTP request containing the search term
     *
     * @return string
     * The validated search term
     *
     *********************************************************************/
    public function validateReturnSearchText(Request $request): string
    {
        $searchTerm = $request->validate(['search.term' =>
        'required|min:1|max:100|regex:/^[\pL\pM\pN\s.,:\/\-_&?=]*$/u']);

        return $searchTerm['search']['term'];
    }





    /*********************************************************************
     * NICHE TYPE VALIDATION
     *********************************************************************
     *
     * Validates and returns the niche type from the request.
     * Ensures the niche type meets length and character requirements.
     *
     * @param Request $request
     * The HTTP request containing the niche type
     *
     * @return string
     * The validated niche type
     *
     *********************************************************************/
    public function validateReturnNicheType(Request $request): string
    {
        $niche = $request->validate(['niche.type' => 'min:2|max:20|alpha']);
        return $niche['niche']['type'];
    }





    /*********************************************************************
     * PRICE RANGE VALIDATION
     *********************************************************************
     *
     * Validates and returns the price range from the request.
     * Sets default values if min/max prices are not provided.
     *
     * @param Request $request
     * The HTTP request containing price range filters
     *
     * @return array
     * Array containing min and max price values
     *
     *********************************************************************/
    public function validateReturnPrice(Request $request): array
    {
        // -----------------------
        // Validate Price Range
        $filterPrice = $request->validate([
            'filters.price.min' => 'min:0|max:10000|numeric',
            'filters.price.max' => 'min:0|max:10000|numeric',
        ]);

        // -----------------------
        // Set Default Values
        $price['min'] = $request['filters']['price']['min'] ?? 0;
        $price['max'] = $request['filters']['price']['max'] ?? 100;

        return $price;
    }





    /*********************************************************************
     * TABLE SORTING VALIDATION
     *********************************************************************
     *
     * Validates and returns the sorting parameters from the request.
     * Ensures valid sort order and column names.
     *
     * @param Request $request
     * The HTTP request containing sorting parameters
     *
     * @return array
     * Array containing sort order and column
     *
     *********************************************************************/
    public function validateReturnSorting(Request $request): array
    {
        // -----------------------
        // Validate Sorting Parameters
        $sorting = $request->validate([
            'table.sort.order' => 'min:1|max:50|alpha',
            'table.sort.column' => 'min:1|max:100|alpha',
        ]);

        // -----------------------
        // Set Sort Parameters
        $sort['order'] = $sorting['table']['sort']['order'];
        $sort['column'] = $sorting['table']['sort']['column'];

        // -----------------------
        // Set Default Order
        if (! ($sort['order'] === 'asc' || $sort['order'] === 'desc')) {
            $sort['order'] = 'desc';
        }

        return $sort;
    }





    /*********************************************************************
     * DOMAIN RANK VALIDATION
     *********************************************************************
     *
     * Validates and returns the domain rank range from the request.
     * Sets default values if min/max DR are not provided.
     *
     * @param Request $request
     * The HTTP request containing DR range filters
     *
     * @return array
     * Array containing min and max DR values
     *
     *********************************************************************/
    public function validateReturnDR(Request $request): array
    {
        // -----------------------
        // Validate DR Range
        $filterDR = $request->validate([
            'filters.dr.min' => 'min:0|max:100|numeric',
            'filters.dr.max' => 'min:0|max:100|numeric',
        ]);

        // -----------------------
        // Set Default Values
        $dr['min'] = $filterDR['filters']['dr']['min'] ?? 0;
        $dr['max'] = $filterDR['filters']['dr']['max'] ?? 100;

        return $dr;
    }





    /*********************************************************************
     * TRAFFIC VOLUME VALIDATION
     *********************************************************************
     *
     * Validates and returns the traffic volume range from the request.
     * Sets default values if min/max traffic are not provided.
     *
     * @param Request $request
     * The HTTP request containing traffic range filters
     *
     * @return array
     * Array containing min and max traffic values
     *
     *********************************************************************/
    public function validateReturnTraffic(Request $request): array
    {
        // -----------------------
        // Validate Traffic Range
        $traffic = $request->validate([
            'filters.traffic.min' => 'min:0|max:10000000000|numeric',
            'filters.traffic.max' => 'min:0|max:10000000000|numeric',
        ]);

        // -----------------------
        // Set Default Values
        $traffic['min'] = $traffic['filters']['traffic']['min'] ?? 0;
        $traffic['max'] = $traffic['filters']['traffic']['max'] ?? 100000000000;

        return $traffic;
    }





    /*********************************************************************
     * SPAM SCORE VALIDATION
     *********************************************************************
     *
     * Validates and returns the spam score range from the request.
     * Sets default values if min/max spam scores are not provided.
     *
     * @param Request $request
     * The HTTP request containing spam score range filters
     *
     * @return array
     * Array containing min and max spam score values
     *
     *********************************************************************/
    public function validateReturnSpamscore(Request $request): array
    {
        // -----------------------
        // Validate Spam Score Range
        $spamScore = $request->validate([
            'filters.spamScore.min' => 'min:0|max:100|numeric',
            'filters.spamScore.max' => 'min:0|max:100|numeric',
        ]);

        // -----------------------
        // Set Default Values
        $spamScore['min'] = $spamScore['filters']['spamScore']['min'] ?? 0;
        $spamScore['max'] = $spamScore['filters']['spamScore']['max'] ?? 100;

        return $spamScore;
    }





    /*********************************************************************
     * COUNTRY LIST VALIDATION
     *********************************************************************
     *
     * Validates and returns the list of selected country IDs from the request.
     * Ensures the list is an array of numeric values.
     *
     * @param Request $request
     * The HTTP request containing selected country IDs
     *
     * @return array
     * Array of validated country IDs
     *
     *********************************************************************/
    public function validateReturnCountryList(Request $request): array
    {
        // -----------------------
        // Validate Country List
        $countriesList = $request->validate([
            'filters.country.selected' => 'required|array',
            'filters.country.selected.*' => 'required|numeric',
        ]);

        return $countriesList['filters']['country']['selected'];
    }





    /*********************************************************************
     * LANGUAGE LIST VALIDATION
     *********************************************************************
     *
     * Validates and returns the list of selected language IDs from the request.
     * Ensures the list is an array of numeric values.
     *
     * @param Request $request
     * The HTTP request containing selected language IDs
     *
     * @return array
     * Array of validated language IDs
     *
     *********************************************************************/
    public function validateReturnLanguageList(Request $request): array
    {
        // -----------------------
        // Validate Language List
        $languageList = $request->validate([
            'filters.language.selected' => 'required|array',
            'filters.language.selected.*' => 'required|numeric',
        ]);

        return $languageList['filters']['language']['selected'];
    }





    /*********************************************************************
     * CATEGORY LIST VALIDATION
     *********************************************************************
     *
     * Validates and returns the list of selected category IDs from the request.
     * Ensures the list is an array of numeric values.
     *
     * @param Request $request
     * The HTTP request containing selected category IDs
     *
     * @return array
     * Array of validated category IDs
     *
     *********************************************************************/
    public function validateReturnCategoryList(Request $request): array
    {
        // -----------------------
        // Validate Category List
        $categoryList = $request->validate([
            'filters.category.selected' => 'required|array',
            'filters.category.selected.*' => 'required|numeric',
        ]);

        return $categoryList['filters']['category']['selected'];
    }
}
