<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Controllers\MarketplaceMetaDataController;
use App\Http\Requests\AdminUserIndexRequest;
use App\Http\Requests\AdminUserRequest;
use App\Models\User;
use Domain\User\Services\{UserQueryService, UserStatsService};
use Domain\User\User as UserService;
use Illuminate\Http\Request;
use Inertia\Inertia;

class AdminUserController extends Controller
{
    public function __construct(
        private UserService $userService,
        private UserStatsService $userStatsService,
        private UserQueryService $userQueryService,
    ) {}

    /*********************************************************************
     * ADMIN USERS INDEX PAGE
     *********************************************************************
     *
     * Retrieves and filters users with pagination and sorting options.
     *
     * Supports filtering by keyword (name or email), role, and pagination.
     * Allows sorting by id, name, email, or role with default sorting
     * ..by id in descending order.
     *
     * @param AdminUserIndexRequest $request
     * HTTP request containing filter and pagination parameters
     *
     * @return \Inertia\Response
     * Renders the users index page with filtered and paginated users
     *
     *********************************************************************/
    public function index(AdminUserIndexRequest $request)
    {
        // -----------------------
        // Apply Filters
        $filters = array_merge([
            'trashed' => 'all',
        ], $request->validated());

        $users = $this->userStatsService->getFilteredUsers($filters);


        // -----------------------
        // Return Response
        return Inertia::render('Admin/Users/<USER>', [
            'users' => $users,
            'filters' => $filters,
        ]);
    }


    /*********************************************************************
     * ADMIN USER CREATE PAGE
     *********************************************************************
     *
     * Prepares and renders the user creation form with a list of countries.
     *
     * Retrieves all countries ordered by name and passes them to the
     * ..user form view along with the 'create' mode for rendering.
     *
     * @return \Inertia\Response
     * Renders the user creation form with countries data
     *
     *********************************************************************/
    public function create()
    {
        $countriesList = MarketplaceMetaDataController::countriesList();

        return Inertia::render('Admin/Users/<USER>', [
            'countries' => $countriesList,
            'mode' => 'create',
        ]);
    }


    /*********************************************************************
     * ADMIN USER STORE ACTION
     *********************************************************************
     *
     * Handles the creation of a new user.
     *
     * Validates the request data using the AdminUserRequest and creates
     * ..a new user with the provided data, including hashed password.
     *
     * @param AdminUserRequest $request
     * HTTP request containing validated user data
     *
     * @return \Illuminate\Http\RedirectResponse
     * Redirects to users index with success message
     *
     *********************************************************************/
    public function store(AdminUserRequest $request)
    {
        $this->userService->createUser($request->toDto());

        return redirect()
            ->route('admin.users.index')
            ->with('success', 'User created successfully.');
    }


    /*********************************************************************
     * ADMIN USER SHOW PAGE
     *********************************************************************
     *
     * Displays detailed information about a user including their profile,
     * ..transactions, statistics, and websites.
     *
     * Loads user relationships and retrieves associated data for
     * ..comprehensive user overview display.
     *
     * @param User $user
     * The user model instance to display
     *
     * @return \Inertia\Response
     * Renders the user details page with comprehensive user data
     *
     *********************************************************************/
    public function show(User $user)
    {
        // -----------------------
        // Load User Relationships
        $user->load(['country', 'websites']);


        // -----------------------
        // Retrieve User Data
        $cartItems = $user->cartItems()->get();
        $walletBalance = $user->balance;
        $transactions = $this->userQueryService->getUserTransactions($user);
        $userStats = $this->userQueryService->getUserStats($user);
        $websites = $this->userQueryService->getUserWebsites($user);


        // -----------------------
        // Return Response
        return Inertia::render('Admin/Users/<USER>/Index', [
            'user' => $user,
            'balance' => $walletBalance,
            'transactions' => $transactions,
            'stats' => $userStats['stats'],
            'advertiser_order_stats' => $userStats['advertiser_order_stats'],
            'publisher_order_stats' => $userStats['publisher_order_stats'],
            'cart_items' => $cartItems,
            'websites' => $websites,
        ]);
    }


    /*********************************************************************
     * ADMIN USER EDIT PAGE
     *********************************************************************
     *
     * Shows the form for editing the specified user.
     *
     * Prepares user data for frontend compatibility by extracting
     * ..address fields from address_data structure.
     *
     * @param User $user
     * The user model instance to edit
     *
     * @return \Inertia\Response
     * Renders the user edit form with user data and countries list
     *
     *********************************************************************/
    public function edit(User $user)
    {
        $countriesList = MarketplaceMetaDataController::countriesList();

        return Inertia::render('Admin/Users/<USER>', [
            'user' => $user,
            'countries' => $countriesList,
            'mode' => 'edit',
        ]);
    }


    /*********************************************************************
     * ADMIN USER UPDATE ACTION
     *********************************************************************
     *
     * Updates the specified user with validated request data.
     *
     * Validates the request data using AdminUserRequest and updates
     * ..the user record with the provided information.
     *
     * @param AdminUserRequest $request
     * HTTP request containing validated user update data
     *
     * @param User $user
     * The user model instance to update
     *
     * @return \Illuminate\Http\RedirectResponse
     * Redirects to users index with success message
     *
     *********************************************************************/
    public function update(AdminUserRequest $request, User $user)
    {

        $this->userService->updateUser($user, $request->toDto());

        return redirect()
            ->route('admin.users.index')
            ->with('success', 'User updated successfully.');
    }


    /*********************************************************************
     * ADMIN USER SOFT DELETE
     *********************************************************************
     *
     * Soft deletes the specified user by marking it as deleted
     * ..without permanently removing from database.
     *
     * @param User $user
     * The user model instance to soft delete
     *
     * @return \Illuminate\Http\RedirectResponse
     * Redirects to users index with success message
     *
     *********************************************************************/
    public function destroy(User $user)
    {
        $this->userService->softDeleteUser($user);

        return redirect()
            ->route('admin.users.index')
            ->with('success', 'User deleted successfully.');
    }


    /*********************************************************************
     * ADMIN USER RESTORE
     *********************************************************************
     *
     * Restores a soft-deleted user by removing the deleted_at timestamp
     * ..and making the user active again.
     *
     * @param User $user
     * The soft-deleted user model instance to restore
     *
     * @return \Illuminate\Http\RedirectResponse
     * Redirects to users index with success message
     *
     *********************************************************************/
    public function restore(User $user)
    {
        $this->userService->restoreUser($user);

        return redirect()
            ->route('admin.users.index')
            ->with('success', 'User restored successfully.');
    }


    /*********************************************************************
     * ADMIN USER PERMANENT DELETE
     *********************************************************************
     *
     * Permanently deletes the specified user from the database.
     *
     * This action is irreversible and completely removes the user
     * ..record and all associated data.
     *
     * @param User $user
     * The user model instance to permanently delete
     *
     * @return \Illuminate\Http\RedirectResponse
     * Redirects to users index with success message
     *
     *********************************************************************/
    public function delete(User $user)
    {
        $this->userService->forceDeleteUser($user);

        return redirect()
            ->route('admin.users.index')
            ->with('success', 'User permanently deleted successfully.');
    }


    /*********************************************************************
     * ADMIN USER ACTIVITY LOGS
     *********************************************************************
     *
     * Displays user activity logs with support for both JSON and
     * ..Inertia responses.
     *
     * Returns JSON response for AJAX requests and Inertia response
     * ..for regular page requests.
     *
     * @param User $user
     * The user model instance to get logs for
     *
     * @param Request $request
     * HTTP request to determine response format
     *
     * @return \Illuminate\Http\JsonResponse|\Inertia\Response
     * Returns logs data in requested format
     *
     *********************************************************************/
    public function activityLogs(User $user, Request $request)
    {
        $logs = $this->userService->getUserActivityLogs($user);

        if ($request->wantsJson() || $request->ajax()) {
            return response()->json([
                'props' => [
                    'logs' => $logs,
                ],
            ]);
        }

        return Inertia::render('Admin/Users/<USER>', [
            'logs' => $logs,
            'user_id' => $user->id,
        ]);
    }
}
