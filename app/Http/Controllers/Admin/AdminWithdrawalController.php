<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\WithdrawalApproveRequest;
use App\Http\Resources\MediaResource;
use Domain\Withdrawal\AdminWithdrawalService;
use Inertia\Inertia;
use Inertia\Response;

class AdminWithdrawalController extends Controller
{
    public function __construct(
        private AdminWithdrawalService $withdrawalService,
    ) {}

    /**********************************************************************
     * LIST WITHDRAWAL REQUESTS
     **********************************************************************
     *
     * Retrieves all withdrawal requests with related user data.
     *
     **********************************************************************/
    public function index(): Response
    {
        $withdrawals = $this->withdrawalService->getWithdrawals();

        return Inertia::render('Admin/Withdrawals/Index', [
            'withdrawals' => $withdrawals,
        ]);
    }



    /**********************************************************************
     * SHOW WITHDRAWAL REQUEST
     **********************************************************************
     *
     * Retrieves and displays detailed withdrawal request information.
     *
     * @param int $id
     * The ID of the withdrawal request to display
     *
     * @return \Inertia\Response
     * Renders the withdrawal request show page with the specified
     * ...withdrawal request data
     **********************************************************************/
    public function show($id): Response
    {
        $data = $this->withdrawalService->getWithdrawal($id);

        return Inertia::render('Admin/Withdrawals/Show', [
            'withdrawal' => $data['withdrawal'],
            'user' => $data['withdrawal']->user,
            'paymentMethod' => $data['withdrawal']->paymentMethod,
            'payments' => $data['payments'],
            'media' => MediaResource::collection($data['withdrawal']->media),
        ]);
    }

    /**********************************************************************
     * APPROVE WITHDRAWAL REQUEST
     **********************************************************************
     *
     * Approves a withdrawal request and updates the withdrawal request status to "approved".
     *
     **********************************************************************/
    public function approve(WithdrawalApproveRequest $request, $id): void
    {
        $this->withdrawalService->approveWithdrawal($id, $request->validated());
    }
}
