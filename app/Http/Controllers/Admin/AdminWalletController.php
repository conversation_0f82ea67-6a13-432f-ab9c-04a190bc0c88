<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\CreditWalletRequest;
use App\Models\MarketplaceOrder;
use App\Models\WalletWithdrawalRequest;
use Bavix\Wallet\Models\Transaction;
use Domain\Wallet\Services\CreditService;
use Inertia\Inertia;

class AdminWalletController extends Controller
{
    public function __construct(
        private CreditService $creditService,
    ) {}

    /**********************************************************************
     * ADMIN WALLET INDEX PAGE
     **********************************************************************
     *
     * Retrieves and displays all wallet transactions with pagination.
     *
     *********************************************************************/
    public function index()
    {
        // Get recent transactions (paginated)
        $transactions = Transaction::with('wallet', 'wallet.holder')
            ->orderBy('id', 'desc')
            ->paginate(15)
            ->through(fn($transaction) => [
                'id' => $transaction->id, //id of the transaction
                'type' => $transaction->type, //type of the transaction
                'amount' => $transaction->amount, //amount of the transaction
                'user' => $transaction->wallet->holder, //user who made the transaction
                'order_id' => $transaction->meta['order_id'] ?? 'N/A', //order id if transaction is related to an order
                'reference' => $transaction->meta['reference'] ?? 'N/A', // reference if transaction is related to a reference
                'status' => $transaction->confirmed ? 'Approved' : 'Pending', // status of the transaction
                'date' => $transaction->created_at->format('Y-m-d H:i:s'), // date of the transaction
                'debit' => $transaction->type === 'withdraw' ? number_format($transaction->amount / 100, 2) : 0, // debit amount
                'credit' => $transaction->type === 'deposit' ? number_format($transaction->amount / 100, 2) : 0, // credit amount
            ]);

        // Get transaction statistics
        $stats = [
            'total_deposits' => number_format(MarketplaceOrder::sum('price_paid'), 2),
            'total_withdrawals' => number_format(WalletWithdrawalRequest::sum('amount') / 100, 2),
            'pending_transactions' => Transaction::where('confirmed', false)
                ->count(),
        ];


        return Inertia::render('Admin/Wallet/Index', [
            'transactions' => $transactions,
            'stats' => $stats,
        ]);
    }





    /**********************************************************************
     * ADMIN PAYMENT SHOW PAGE
     **********************************************************************
     *
     * Retrieves and displays detailed payment information including
     * ...user and order details.
     *
     * Loads user and order data with eager loading of order items count.
     *
     * @param MarketplacePayment $payment The payment to display details for
     * @return \Inertia\Response Renders the admin payment show page with payment details
     *
     *********************************************************************/
    public function show(Transaction $transaction)
    {
        return Inertia::render('Admin/Wallet/Show', [
            'transaction' => $transaction,
        ]);
    }



    /**********************************************************************
     * ADMIN WALLET CREDIT PAGE
     **********************************************************************
     *
     * Retrieves and displays the admin wallet credit page with users list.
     *
     *********************************************************************/
    public function credit()
    {
        $users = $this->creditService->getUsersForCredit();
        $recentCredits = $this->creditService->getRecentCredits();


        return Inertia::render('Admin/Wallet/Credit', [
            'users' => $users,
            'recentCredits' => $recentCredits,
        ]);
    }

    /**********************************************************************
     * CREDIT USER WALLET
     **********************************************************************
     *
     * Credits a user's wallet with the specified amount.
     *
     * @param CreditWalletRequest $request
     * @return \Illuminate\Http\RedirectResponse
     *
     *********************************************************************/
    public function creditWallet(CreditWalletRequest $request)
    {
        $success = $this->creditService->creditUserWallet(
            $request->validated(),
        );

        if ($success) {
            return redirect()->back()
                ->with('success', 'Wallet credited successfully.');
        }

        return redirect()->back()
            ->with('error', 'Failed to credit wallet. Please try again.');
    }
}
