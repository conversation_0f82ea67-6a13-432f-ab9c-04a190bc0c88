<?php

namespace App\Http\Controllers;

use Domain\Cart\Cart as CartService;
use Domain\Cart\Requests\CreateCartRequest;
use Domain\Cart\Requests\UpdateCartRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class MarketplaceCartController extends Controller
{
    /*************************************************
     * Constructor
    /************************************************/
    public function __construct(
        private CartService $cartService,
    ) {}

    /*****************************************
     *
     * SHOW CART PAGE
     *
     * Redirect back if cart is empty
     * Return cart page with user data
     *
    /*****************************************/
    public function cartPage(Request $request)
    {
        $cartData = CartService::getUserCart();

        return view('marketplace.cart', [
            'cartItems' => $cartData['items'],
            'cartStats' => $cartData['stats'],
            'userData' => Auth::user(),
        ])->fragmentIf($request->fragment === 'cart', 'cart-content');
    }





    /*****************************************
     *
     * ADD ITEM TO CART
     *
     * Get website id and create a cart data
     * row in db
     *
    /*****************************************/
    public function add(CreateCartRequest $request): bool
    {
        return $this->cartService->addToCart($request->toDto());
    }





    /*****************************************
     *
     * UPDATE CART ITEMS
     *
     * 1. Niche Change
     * 2. Delete Item from cart
     *
     ****************************************/
    public function update(UpdateCartRequest $request): bool
    {
        return $this->cartService->updateCart($request->toDto());
    }
}
