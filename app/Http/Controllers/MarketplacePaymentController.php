<?php

namespace App\Http\Controllers;

use Domain\Cart\Cart as CartService;
use Domain\CheckoutSession\Address\Address;
use Domain\CheckoutSession\Address\Requests\AddressUpdateRequest;
use Domain\CheckoutSession\CheckoutSession;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;

class MarketplacePaymentController extends Controller
{
    public $stripePublishableKey;

    /*******************************************************************
     * CONSTRUCTOR
     *********************************************************************
     *
     * Initializes the controller and sets up middleware.
     *
     *******************************************************************/
    public function __construct(
        private CartService $cartService,
        private Address $address,
        private CheckoutSession $checkoutSession,
    ) {
        $this->stripePublishableKey = config('services.stripe.publishable');
    }





    /*******************************************************************
     * BILLING ADDRESS PAGE
     *********************************************************************
     *
     * Displays the billing address collection page for checkout.
     *
     * - Retrieves user's cart data and available countries
     * - Redirects to cart if cart is empty
     * - Returns view with cart items, stats, countries list and user data
     * - Supports fragment-based rendering for address section
     *
     * @param Request $request
     * HTTP request containing fragment parameter for conditional rendering
     *
     * @return \Illuminate\View\View|\Illuminate\Http\RedirectResponse
     * Returns address collection view or redirects to cart if empty
     *
     *******************************************************************/
    public function addressPage(Request $request)
    {
        // -----------------------
        // Get Cart and Countries Data
        $cartData = CartService::getUserCart();
        $countriesList = MarketplaceMetaDataController::countriesList();

        // -----------------------
        // Validate Cart
        if ($cartData['stats']['count'] < 1) {
            return redirect()->route('cart');
        }
        // -----------------------
        // Return View
        return view('marketplace.address-collect', [
            'cartItems' => $cartData['items'],
            'cartStats' => $cartData['stats'],
            'countriesList' => $countriesList,
            'userData' => Auth::user(),
        ])->fragmentIf($request->fragment === 'address', 'address-content');

    }








    /*******************************************************************
     * PAYMENT COLLECTION PAGE
     *********************************************************************
     *
     * Displays the payment collection page with Stripe integration.
     *
     * - Creates checkout session
     * - Retrieves Stripe publishable key
     * - Returns view with cart data and Stripe configuration
     * - Supports fragment-based rendering for payment section
     *
     * @param Request $request
     * HTTP request containing fragment parameter for conditional rendering
     *
     * @return \Illuminate\View\View
     * Returns payment collection view with Stripe integration
     *
     *******************************************************************/
    public function paymentPage()
    {
        // -----------------------
        // Initialize Checkout Session
        $cartData = $this->checkoutSession->createCheckoutSession();

        // -----------------------
        // Return View
        return view('marketplace.payment-collect', [
            'cartItems' => $cartData['items'],
            'cartStats' => $cartData['stats'],
            'stripePublishableKey' => $this->stripePublishableKey,
            'clientSecret' => $cartData['stripeClientSecret'],
        ]);
    }





    /*******************************************************************
     * PROCESS PAYMENT SUCCESS
     *********************************************************************
     *
     * Handles successful payment processing and order creation.
     *
     * - Validates payment intent
     * - Updates checkout session
     * - Creates payment record
     * - Creates order and order items
     * - Removes items from cart
     * - Sends order confirmation
     *
     * @param Request $request
     * HTTP request containing payment_intent parameter
     *
     * @return \Illuminate\View\View|\Illuminate\Http\RedirectResponse
     * Returns success page or redirects to orders if session invalid
     *
     *******************************************************************/
    public function paymentSuccessPage(Request $request)
    {
        // -----------------------
        // Validate Payment Intent
        $request->validate(['payment_intent' => 'required|min:5|max:500']);
        // -----------------------
        // Return Success Page
        return view('marketplace.payment-status', ['orderId' => null]);
    }





    /*******************************************************************
     * PROCESS FRONTEND DATA
     *********************************************************************
     *
     * Handles various frontend data processing requests.
     *
     * - Processes address updates
     * - Redirects to appropriate pages
     *
     * @param Request $request
     * HTTP request containing data type and related fields
     *
     * @return \Illuminate\Http\RedirectResponse|null
     * Redirects to appropriate page or returns null
     *
     *******************************************************************/
    public function dataProcess(AddressUpdateRequest $request)
    {

        // -----------------------
        // Process Address Update
        switch ($request->data) {
            case 'address':
                // -----------------------
                // Update Address
                $updateAddress = $this->address->updateAddress($request->validated(), Auth::user());
                // -----------------------
                // Save Order Memo To Session
                Session::put('order_memo', $request->order_memo);
                // -----------------------
                if ($updateAddress) {
                    return redirect()->route('payment-collect')->with('success', 'Address updated successfully');
                }

                return redirect()->back()->with('error', 'Failed to update address');
            default:
                return;
        }
    }
}
