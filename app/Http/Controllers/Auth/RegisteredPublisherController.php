<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Controllers\MarketplaceMetaDataController;
use App\Models\MarketplaceWebsite;
use App\Models\User;
use App\Rules\ValidWebsiteEligibility;
use App\Rules\WebsiteValidationRule;
use App\Services\AllowedDomains;
use EragLaravelDisposableEmail\Rules\DisposableEmailRule;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Laravel\Fortify\Contracts\LoginResponse;

class RegisteredPublisherController extends Controller
{
    /**********************************************************************
     * SHOW PUBLISHER REGISTRATION FORM
     **********************************************************************
     *
     * Displays the registration form for new publishers.
     *
     * Retrieves a list of available countries from MarketplaceMetaDataController
     * ..and passes them to the Blade view for rendering.
     *
     * @return \Illuminate\View\View
     * Renders the publisher registration form with country options
     *
     **********************************************************************/
    public function create()
    {
        $countries = MarketplaceMetaDataController::countriesList();

        return view('auth.register-publisher', compact('countries'));
    }






    /**********************************************************************
     * HANDLE PUBLISHER REGISTRATION (REFACTOR ‼️)
     **********************************************************************
     *
     * Validates and registers a new publisher user.
     *
     * Enforces disposable email blocking, domain validation against email,
     * ..and public domain restrictions using custom rules and services.
     *
     * Validate domain for email and primary domain from services
     *
     * On successful registration:
     * - Creates the user
     * - Fires the Registered event
     * - Logs in the user automatically
     *
     * @param Request $request
     * The HTTP request containing registration data
     *
     * @param LoginResponse $loginResponse
     * Laravel Fortify login response to redirect the user after registration
     *
     * @return \Symfony\Component\HttpFoundation\Response
     * Redirects the user to the intended post-registration destination
     *
     **********************************************************************/

    public function store(Request $request, LoginResponse $loginResponse)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => [
                'required',
                'email',
                'max:255',
                Rule::unique('users', 'email'),
                new DisposableEmailRule(),
                function ($attribute, $value, $fail): void {
                    $emailDomain = Str::lower(Str::after($value, '@'));
                    if (AllowedDomains::isBanned($emailDomain)) {
                        $fail(_('Please use a business email address, not a public provider like Gmail or Yahoo.'));
                    }
                    // Check if email domain matches any existing website domain
                    $exists = MarketplaceWebsite::where('website_domain', $emailDomain)->exists();
                    if ($exists) {
                        $fail(_('The website domain is already registered. Please use a different email address.'));
                    }
                },
            ],
            'country_id' => ['required', 'exists:countries_list,id'],
            'password' => ['required', 'string', 'confirmed', 'min:8'],
            'primary_domain' => [
                'required',
                'string',
                'max:255',
                Rule::unique('users', 'primary_domain'),
                new WebsiteValidationRule($request->input('email')),
                new ValidWebsiteEligibility(),
            ],
        ]);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => 'publisher',
            'primary_domain' => $request->primary_domain,
            'country_id' => $request->country_id,
        ]);

        // If using Spatie Roles
        // $user->assignRole('publisher');

        event(new Registered($user));
        Auth::login($user);

        return $loginResponse->toResponse($request);
    }
}
