<?php

namespace App\Http\Controllers;

use Domain\Order\Modules\OrdersTraits;
use Domain\Order\Requests\OrderDetailRequest;

class MarketplaceOrderController extends Controller
{
    use OrdersTraits;



    /*******************************************************************
     * USER ORDERS LIST PAGE
     *********************************************************************
     *
     * Displays a paginated list of orders for the authenticated user.
     * Orders are sorted by latest first and paginated with 10 items per page.
     *
     * @Author: <PERSON><PERSON> Ahmed
     *
     * @return \Illuminate\View\View
     * Returns the orders history view with paginated orders data
     *
     *******************************************************************/
    public function ordersList()
    {
        $orders = $this->getLatestOrdersListWithPagination(['orderItems']);

        return view('marketplace.orders-history', ['orders' => $orders]);
    }





    /*******************************************************************
     * ORDER DETAILS PAGE
     *********************************************************************
     *
     * Displays detailed information about a specific order.
     * Includes order status and all related order items.
     *
     * @Author: Hamza Ahmed
     *
     * @param Request $request
     * HTTP request containing the order ID
     *
     * @return \Illuminate\View\View
     * Returns the order details view with order and status data
     *
     *******************************************************************/
    public function orderDetails(OrderDetailRequest $request)
    {
        $order = $this->getOrderById($request->validated('id'), ['orderItems']);

        return view('marketplace.order-single-details', [
            'order' => $order,
            'orderStatus' => $order->status,
        ]);
    }




    /*******************************************************************
     * ORDER INVOICE PAGE
     *********************************************************************
     *
     * Displays the invoice for a specific order.
     * Verifies user authorization before showing invoice.
     *
     * @param Request $request
     * HTTP request containing the order ID
     *
     * @return \Illuminate\View\View
     * Returns the invoice view with order and user data
     *
     *******************************************************************/
    public function showInvoice(OrderDetailRequest $request)
    {

        // -----------------------
        // Fetch Order
        $order = $this->getOrderById($request->validated('id'), ['orderItems', 'user', 'payment.checkoutSession']);

        return view('app.invoices.invoice', [
            'order' => $order,
            'user' => $order->user,
            'order_memo' => $order->payment->checkoutSession->order_memo ?? 'No Note',
        ]);
    }
}
