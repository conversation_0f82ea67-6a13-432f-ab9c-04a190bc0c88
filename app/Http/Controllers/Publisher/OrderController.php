<?php

namespace App\Http\Controllers\Publisher;

use App\Http\Controllers\Controller;
use App\Models\MarketplaceSingleOrderItem;
use App\Services\Publisher\PublisherOrderService;
use App\States\OrderItem\ContentPending;
use App\States\OrderItem\ContentRevisionRequestedByAdvertiser;
use App\States\OrderItem\OrderItemCancelled;
use App\States\OrderItem\PublicationDelivered;
use App\States\OrderItem\PublicationInProcess;
use App\States\OrderItem\RequirementRevisionRequested;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class OrderController extends Controller
{
    public function __construct(
        private PublisherOrderService $publisherOrderService,
    ) {}

    /*********************************************************************
     * ORDERS OVERVIEW
     **********************************************************************
     *
     * Renders the orders overview page with pending and completed orders.
     * Displays order items with their status, details, and customer information.
     *
     * @return \Inertia\Response Renders the orders overview view
     * ..with pending and completed order data
     *
     *********************************************************************/
    public function orders()
    {
        //-------------------------
        // Get Pending Order Items
        $pendingOrderItems = $this->publisherOrderService->getPendingOrderItems();

        //-------------------------
        // Get Completed Order Items
        $completedOrderItems = $this->publisherOrderService->getCompletedOrderItems();

        // -----------------------
        // Render Orders View
        return Inertia::render('Publisher/Orders/Index', [
            'pendingOrderItems' => $pendingOrderItems,
            'completedOrderItems' => $completedOrderItems,
        ]);
    }






    /*********************************************************************
     * ORDER DETAILS
     **********************************************************************
     *
     * Renders the detailed view of a specific order item with all related
     * ..information including requirements, user details, and messages.
     *
     * @param int $id The ID of the order item to display
     * @return \Inertia\Response Renders the order details view
     * ..with complete order and item information
     *
     *********************************************************************/
    public function orderDetails(MarketplaceSingleOrderItem $item)
    {
        $orderDetails = $this->publisherOrderService->getOrderDetails($item);

        // -----------------------
        // Render Order Details View
        return Inertia::render('Publisher/Orders/Details/Index', $orderDetails);
    }





    /*********************************************************************
     * APPROVE ORDER ITEM
     **********************************************************************
     *
     * Transitions an order item to the ContentPending state, indicating
     * ..approval of the order by the publisher.
     *
     * @param MarketplaceSingleOrderItem $item The order item to approve
     * @return \Illuminate\Http\RedirectResponse Redirects back with success
     * ..or error message
     *
     *********************************************************************/
    public function approveOrderItem(MarketplaceSingleOrderItem $item)
    {
        try {
            // -----------------------
            // Transition Order State
            $item->state->transitionTo(ContentPending::class);

            return back()->with('success', 'Order approved successfully');
        } catch (\Exception $e) {
            Log::error('Failed to approve order: ' . $e->getMessage());
            return back()->with('error', 'Failed to approve order: ' . $e->getMessage());
        }
    }





    /*********************************************************************
     * REQUEST REVISION
     **********************************************************************
     *
     * Transitions an order item to the RequirementRevisionRequested state,
     * indicating that the publisher has requested revisions to the requirements.
     *
     * @param MarketplaceSingleOrderItem $item The order item to request revision for
     * @return \Illuminate\Http\RedirectResponse Redirects back with success
     * ..or error message
     *
     *********************************************************************/
    public function requestRevision(MarketplaceSingleOrderItem $item, Request $request)
    {
        try {
            // -----------------------
            // Validate Request Due to validation one  so don't need to create Request class
            $request->validate([
                'reason' => 'required|string|min:10|max:1000',
            ]);

            // -----------------------
            // Update Requirement Reason
            $item->requirements()->update([
                'advertiser_revision_reason' => $request->reason,
            ]);

            // -----------------------
            // Transition Order State
            $item->state->transitionTo(RequirementRevisionRequested::class);

            return back()->with('success', 'Revision requested successfully');
        } catch (\Exception $e) {
            Log::error('Failed to request revision: ' . $e->getMessage());
            return back()->with('error', 'Failed to request revision: ' . $e->getMessage());
        }
    }





    /*********************************************************************
     * REQUEST CONTENT REVISION
     **********************************************************************
     *
     * Transitions an order item to the ContentRevisionRequested state,
     * indicating that the publisher has requested revisions to the content.
     *
     * @param MarketplaceSingleOrderItem $item The order item to request content revision for
     * @return \Illuminate\Http\RedirectResponse Redirects back with success
     * ..or error message
     *
     *********************************************************************/
    public function requestContentRevision(MarketplaceSingleOrderItem $item, Request $request)
    {
        try {
            // -----------------------
            // Validate Request
            $request->validate([
                'reason' => 'required|string|min:10|max:1000',
            ]);

            // -----------------------
            // Update Content Reason
            $item->content()->update([
                'publisher_advertiser_revision_reason' => $request->reason,
            ]);

            // -----------------------
            // Check if Content is Provided by Customer
            $isContentProvidedByCustomer = $item->is_content_provided_by_customer;

            // -----------------------
            // Transition Order State
            if ($isContentProvidedByCustomer === 0) {
                $item->state->transitionTo(ContentRevisionRequestedByAdvertiser::class);
            } else {
                $item->state->transitionTo(ContentPending::class);
            }

            $item->save();

            return back()->with('success', 'Content revision requested successfully');
        } catch (\Exception $e) {
            return back()->with('error', 'Failed to request content revision: ' . $e->getMessage());
        }
    }





    /*********************************************************************
     * APPROVE CONTENT
     **********************************************************************
     *
     * Transitions an order item to the ContentPending state, indicating
     * ..approval of the order by the publisher.
     *
     * @param MarketplaceSingleOrderItem $item The order item to approve
     * @return \Illuminate\Http\RedirectResponse Redirects back with success
     * ..or error message
     *
     *********************************************************************/
    public function approveContent(MarketplaceSingleOrderItem $item)
    {
        try {
            // -----------------------
            // Transition Order State
            $item->state->transitionTo(PublicationInProcess::class);
            $item->save();
            return back()->with('success', 'Content approved successfully');
        } catch (\Exception $e) {
            return back()->with('error', 'Failed to approve content: ' . $e->getMessage());
        }
    }





    /*********************************************************************
     * PUBLISH ORDER ITEM
     **********************************************************************
     *
     * Transitions an order item to the PublicationInProcess state, indicating
     * ..that the order has been published.
     *
     * @param MarketplaceSingleOrderItem $item The order item to publish
     * @return \Illuminate\Http\RedirectResponse Redirects back with success
     * ..or error message
     *
     *********************************************************************/
    public function publish(MarketplaceSingleOrderItem $item, Request $request)
    {
        try {
            // -----------------------
            // Validate Request
            $request->validate([
                'published_url' => 'required|url|max:255',
            ]);

            // -----------------------
            // Update Content URL
            $item->publication()->updateOrCreate(
                [
                    'order_item_id' => $item->id,
                ],
                [
                    'publication_url' => $request->published_url,
                    'marketplace_website_id' => $item->website->id,
                    'active' => true,
                ],
            );



            // -----------------------
            // Update Order Item State
            $item->state->transitionTo(PublicationDelivered::class);
            $item->save();

            return back()->with('success', 'Published URL submitted successfully');
        } catch (\Exception $e) {
            return back()->with('error', 'Failed to submit published URL: ' . $e->getMessage());
        }
    }





    /*********************************************************************
     * CANCEL ORDER ITEM
     **********************************************************************
     *
     * Transitions an order item to the Cancelled state, indicating that
     * ..the order has been cancelled.
     *
     * @param MarketplaceSingleOrderItem $item The order item to cancel
     * @param Request $request The request containing cancellation reason
     * @return \Illuminate\Http\RedirectResponse Redirects back with success
     * ..or error message
     *
     *********************************************************************/
    public function cancelOrderItem(MarketplaceSingleOrderItem $item, Request $request)
    {
        try {
            // -----------------------
            // Validate Request
            $request->validate([
                'reason' => 'required|string|min:10|max:1000',
            ]);

            // -----------------------
            // Update Cancellation Reason
            $item->update([
                'reason' => $request->reason,
            ]);

            // -----------------------
            // Transition Order State
            $item->state->transitionTo(OrderItemCancelled::class);

            return back()->with('success', 'Order cancelled successfully');
        } catch (\Exception $e) {
            return back()->with('error', 'Failed to cancel order: ' . $e->getMessage());
        }
    }
}
