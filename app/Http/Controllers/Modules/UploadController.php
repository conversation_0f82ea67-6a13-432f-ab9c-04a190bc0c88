<?php

namespace App\Http\Controllers\Modules;

use App\Http\Controllers\Controller;
use App\Models\Media;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class UploadController extends Controller
{
    /*********************************************************************
     * FILE UPLOAD HANDLER
     *********************************************************************
     *
     * <PERSON><PERSON> file uploads with validation and storage
     * - Validates file size and type
     * - Stores file in public disk
     * - Creates media record in database
     * - Returns file details and URL
     *
     * @param Request $request
     * HTTP request containing the file to upload
     *
     * @return \Illuminate\Http\JsonResponse
     * JSON response with upload status and file details
     *
     *********************************************************************/
    public function upload(Request $request)
    {
        //-------------------------
        // Validate Upload Request
        $request->validate([
            'file' => 'required|file|max:10240', // 10MB max
            'file.*' => 'mimes:png,jpg,jpeg,pdf',
        ]);


        //-----------------------
        // Process File Upload
        if ($request->hasFile('file')) {
            $file = $request->file('file');
            try {
                $path = $file->store('uploads', 'r2');

                if (! $path) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Failed to store file',
                    ], 500);
                }

                //-----------------------
                // Create Media Record
                $media = Media::create([
                    'user_id' => Auth::id(),
                    'meta' => [
                        'name' => $file->getClientOriginalName(),
                        'size' => $file->getSize(),
                    ],
                    'path' => $path,
                    'mime_type' => $file->getMimeType(),
                    'disk' => 'r2',
                ]);


                //-------------------------
                // Return Success Response
                return response()->json([
                    'success' => true,
                    'media_id' => $media->id,
                    'path' => $path,
                    'name' => $file->getClientOriginalName(),
                    'size' => $file->getSize(),
                    'url' => Storage::url($path),
                ]);
            } catch (\Exception $e) {
                return response()->json([
                    'success' => false,
                    'message' => 'An error occurred while processing the file',
                ], 500);
            }
        }


        // -----------------------
        // Return Error Response
        return response()->json([
            'success' => false,
            'message' => 'No file uploaded',
        ], 400);
    }
}
