<?php

namespace App\Http\Controllers\Modules;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class NotificationController extends Controller
{
    /*******************************************************************
     * MARK NOTIFICATION AS READ
     *********************************************************************
     *
     * Marks a specific notification as read for the authenticated user.
     *
     * - Finds the notification by ID for the current user
     * - Updates the notification status to read
     * - Returns success response
     *
     * @param Request $request
     * HTTP request object
     *
     * @param int $id
     * The ID of the notification to mark as read
     *
     * @return \Illuminate\Http\JsonResponse
     * Returns JSON response with success status
     *
     *******************************************************************/
    public function markAsRead(Request $request, $id)
    {
        //-----------------------
        // Get Notification
        $notification = Auth::user()->notifications()->findOrFail($id);

        //-----------------------
        // Mark as Read
        $notification->markAsRead();

        return response()->json(['success' => true]);
    }





    /*******************************************************************
     * MARK ALL NOTIFICATIONS AS READ
     *********************************************************************
     *
     * Marks all unread notifications as read for the authenticated user.
     *
     * - Retrieves all unread notifications for current user
     * - Updates all notifications status to read
     * - Returns success response
     *
     * @param Request $request
     * HTTP request object
     *
     * @return \Illuminate\Http\JsonResponse
     * Returns JSON response with success status
     *
     *******************************************************************/
    public function markAllAsRead(Request $request)
    {
        //-----------------------
        // Mark All as Read
        Auth::user()->unreadNotifications->markAsRead();

        return response()->json(['success' => true]);
    }
}
