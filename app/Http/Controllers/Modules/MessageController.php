<?php

namespace App\Http\Controllers\Modules;

use App\Http\Controllers\Controller;
use App\Models\MarketplaceSingleOrderItem;
use App\Services\MessageService;
use Illuminate\Http\Request;

class MessageController extends Controller
{
    // initialize
    protected $messageService;

    public function __construct(MessageService $messageService)
    {
        $this->messageService = $messageService;
    }





    /*********************************************************************
     * MESSAGES - LIST ORDER ITEM MESSAGES
     *********************************************************************
     *
     * Verify user access ‼️🚨
     *
     * Retrieves all messages associated with a specific order item.
     * Messages are returned in JSON format for API consumption.
     *
     * @param int $orderItemId
     * The ID of the order item to fetch messages for
     *
     * @return \Illuminate\Http\JsonResponse
     * Returns a JSON response containing the messages collection
     *
     *********************************************************************/
    public function index($orderItemId)
    {
        // -----------------------
        // Fetch Order Item
        $orderItem = MarketplaceSingleOrderItem::find($orderItemId);

        // -----------------------
        // Get Messages with sender and receiver data
        $messages = $this->messageService->getMessages($orderItem);
        $messages->load(['sender', 'receiver']);

        // -----------------------
        // Return Response
        return response()->json($messages);
    }





    /*********************************************************************
     * MESSAGES - STORE NEW MESSAGE
     *********************************************************************
     *
     *  Verify user permission ‼️🚨
     *
     * Creates and stores a new message for an order item.
     * Supports both text messages and file attachments.
     *
     * - Validates input data including file uploads
     * - Handles both text and attachment messages
     * - Returns created message data
     *
     * @param Request $request
     * HTTP request containing message data and optional file attachment
     *
     * @return \Illuminate\Http\JsonResponse
     * Returns the created message with 201 status code
     *
     *********************************************************************/
    public function store(Request $request)
    {
        // -----------------------
        // Validate Input
        $request->validate([
            'text' => 'required_without:attachment',
            'attachment' => 'nullable|file|max:2048|mimes:jpg,jpeg,png,doc,docx,pdf,txt',
            'order_item_id' => 'required|exists:marketplace_single_order_items,id',
            'sender_id' => 'required|exists:users,id',
            'receiver_id' => 'required|exists:users,id',
        ]);

        // -----------------------
        // Store Message
        $message = $this->messageService->storeMessage(
            $request->only(['text', 'order_item_id', 'sender_id', 'receiver_id']),
            $request->file('attachment'),
        );

        // -----------------------
        // Load sender and receiver data
        $message->load(['sender', 'receiver']);

        // -----------------------
        // Return Response
        return response()->json($message, 201);
    }





    /*********************************************************************
     * MESSAGES - MARK AS READ
     *********************************************************************
     *
     *  Verify user permission ‼️🚨
     *
     *
     * Marks all messages for a specific order item as read.
     * Updates the read status of messages in the database.
     *
     * @param int $orderItemId
     * The ID of the order item whose messages should be marked as read
     *
     * @return \Illuminate\Http\JsonResponse
     * Returns a success response with true status
     *
     *********************************************************************/
    public function markAsRead($orderItemId)
    {
        // -----------------------
        // Mark Messages as Read
        $this->messageService->markMessagesAsRead($orderItemId);

        // -----------------------
        // Return Response
        return response()->json(['success' => true]);
    }
}
