<?php

namespace App\Http\Controllers\Modules;

use App\Http\Controllers\Controller;
use App\Models\MarketplaceWebsiteLanguage;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;

class LocalizationController extends Controller
{
    public function switch($locale)
    {
        // Check if the language exists in our database
        $language = MarketplaceWebsiteLanguage::where('code', $locale)->first();

        if (! $language) {
            return redirect()->back()->with('error', 'Language not found');
        }

        // Set the application locale
        App::setLocale($locale);
        Session::put('locale', $locale);

        return redirect()->back()->with('success', 'Language switched successfully');
    }
}
