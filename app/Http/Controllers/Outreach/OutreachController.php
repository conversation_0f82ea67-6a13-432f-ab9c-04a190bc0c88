<?php

namespace App\Http\Controllers\Outreach;

use App\Http\Controllers\Controller;
use App\Models\Outreach;
use App\Services\OutreachService;
use Illuminate\Http\RedirectResponse;

class OutreachController extends Controller
{
    protected $outreachService;

    /**********************************************************************
     * CONSTRUCTOR
     **********************************************************************
     *
     * Injects the OutreachService for handling outreach-related logic
     * such as website assignment and filtering.
     *
     * @param OutreachService $outreachService
     * Service class responsible for outreach operations
     **********************************************************************/
    public function __construct(OutreachService $outreachService)
    {
        $this->outreachService = $outreachService;
    }



    /**********************************************************************
     * ASSIGN WEBSITE TO OUTREACH
     **********************************************************************
     *
     * Assigns the first unassigned website to the currently authenticated
     * outreach user. This initializes a new outreach record with status
     * set to 'inprogress' and stores the assigned timestamp.
     *
     * If no unassigned websites are available, redirects with error message.
     *
     * @return RedirectResponse
     * Redirects to the admin website index with assigned website ID or error
     **********************************************************************/
    public function assignWebsite()
    {
        //--------------------------------
        // Get the first website that is not assigned to outreach
        $website = $this->outreachService->fetchTopOutreachWebsite();

        //--------------------------------
        // If there is no website assigned to outreach, assign the first website

        if ($website) {
            Outreach::create([
                'marketplace_website_id' => $website->id,
                'user_id' => auth('web')->id(),
                'status' => 'inprogress',
                'assigned_at' => now(),
            ]);

            // pass website id to the website index page
            return redirect()->route('admin.websites.index', ['assigned_website' => $website->id]);
        }

        return redirect()->route('admin.websites.index')->with('error', 'No website found');
    }




}
