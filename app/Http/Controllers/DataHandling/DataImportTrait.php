<?php

namespace App\Http\Controllers\DataHandling;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

trait DataImportTrait
{
    /*****************************************
     * CURL: GET DATA VIA PHP CURL
     *****************************************
     *
     * @param $url string (http request src)
     * @param $request array optional:
     *  -> headers
     *  -> method
     *  -> post_fields
     *  -> array [bool] = convert json to array
     *
     * @return $response | false
    /*****************************************/
    public function curlRequest(string $url, array $request = [])
    {

        $curl = curl_init();

        $options = [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        ];


        // 1. Custom Headers
        if (isset($request['headers'])) {
            $options[CURLOPT_HTTPHEADER] = $request['headers'];
        } else {
            $options[CURLOPT_HTTPHEADER] = $this->browserHeaders;
        }


        // 2. Post/get type
        if (isset($request['method'])) {
            $options[CURLOPT_CUSTOMREQUEST] = $request['method'];
        } else {
            $options[CURLOPT_CUSTOMREQUEST] = 'GET';
        }


        // 3. Post fields
        if (isset($request['post_fields'])) {
            $options[CURLOPT_POSTFIELDS] = $request['post_fields'];
        }


        curl_setopt_array($curl, $options);

        $response = curl_exec($curl);
        curl_close($curl);


        // Check if the request was successful
        if ($response === false) {
            return false;
        }

        // Get the HTTP status code
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

        // Check if the HTTP status code is 200
        if ($httpCode !== 200) {
            return false;
        }


        // if array format is needed
        if (isset($request['array']) && $request['array'] === true) {
            return json_decode($response, true);
        }

        return $response;
    }





    /*****************************************
     * CURL PROXIED REQUEST (Get only)
     *****************************************
     *
     * Using proxyscrape.com
     * - 1m free credits/day (checks needed)
     * - https://dashboard.proxyscrape.com/
     * other/freebies/scraperapi
     *
     * @param $url string (to call)
     * @return $response | false
    *****************************************/
    public function curlProxyRequest(string $url)
    {

        $curl = curl_init();

        curl_setopt_array($curl, [
            CURLOPT_URL => 'https://api.proxyscrape.com/v3/accounts/freebies/scraperapi/request',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => '{
            "url":"' . $url . '",
            "httpResponseBody": true
        }',
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'X-Api-Key: 9a0861cf-57dc-4cff-a194-f9acaa0e4f93',
            ],
        ]);

        // exec & check
        $response = curl_exec($curl);
        curl_close($curl);
        if ($response === false) {
            return false;
        }

        $data = json_decode($response, true);

        if ($data['success'] !== true) {
            return false;
        } //proxy success
        if ($data['data']['statusCode'] !== 200) {
            return false;
        }  //requested url success

        // base64 response decode
        return base64_decode($data['data']['httpResponseBody'], true);
    }





    /*****************************************
     * GET DATA VIA LARAVEL HTTP CLIENT
     *****************************************
     *
     * @param string $url
     * @param array $query
     * @return $data|false
    *****************************************/
    public function guzzleRequest(string $url, array $query = [])
    {
        $headers = $this->browserHeaders;

        $response = Http::retry(3, 100)
            ->withHeaders($headers)
            ->get($url, $query);

        if ($response->ok()) {
            $data = $response->json();
        } else {
            $data = false;
        }

        return $data;
    }





    /******************************************
     * Get Domain HOST only From URL
     *****************************************
     *
     * Get domain if its a url
     *
     * @param $url string
     * @return $host string
    ******************************************/
    public function getDomainFromUrl($url)
    {
        // Ensure the URL has the http or https scheme for parse_url to work correctly
        if (! preg_match('/^http[s]?:\/\//', $url)) {
            $url = 'http://' . $url;
        }

        // Parse the URL to get the host part
        $parsedUrl = parse_url($url);

        // if not true return false
        if (! isset($parsedUrl['host'])) {
            return false;
        }
        $host = $parsedUrl['host'];


        // if not valid return false
        if (filter_var($host, FILTER_VALIDATE_DOMAIN, FILTER_FLAG_HOSTNAME) === false) {
            return false;
        }

        // Remove www. if it exists
        if (strpos($host, 'www.') === 0) {
            $host = substr($host, 4);
        }

        // Return false if tld is not present
        if (! strpos($host, '.')) {
            return false;
        }

        return $host;
    }




    /**********************************************
     * Check If String is Valid URL
     **********************************************
     *
     * return same url if valid else null
     * uses: example url
     *
     * @param string $url
     * @return string $url|null
    ***********************************************/
    public function validateReturnURL($url)
    {

        $validity = filter_var($url, FILTER_VALIDATE_URL) !== false;

        if ($validity) {
            return $url;
        }


    }




    /**********************************************
     * Browser headers for crawling
     * Helps stop blocking
    **********************************************/
    public $browserHeaders = [
        'accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'accept-language' => 'en-US,en-GB;q=0.9,en;q=0.8',
        'cache-control' => 'no-cache',
        'dnt' => '1',
        'pragma' => 'no-cache',
        'priority' => 'u=0, i',
        'sec-ch-ua' => '"Chromium";v="128", "Not;A=Brand";v="24", "Google Chrome";v="128"',
        'sec-ch-ua-mobile' => '?0',
        'sec-ch-ua-platform' => '"macOS"',
        'sec-fetch-dest' => 'document',
        'sec-fetch-mode' => 'navigate',
        'sec-fetch-site' => 'none',
        'sec-fetch-user' => '?1',
        'upgrade-insecure-requests' => '1',
        'user-agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.0.0 Safari/537.36',
    ];


}
