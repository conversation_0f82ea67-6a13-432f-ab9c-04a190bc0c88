<?php

namespace App\Http\Controllers\DataHandling;

use App\Http\Controllers\Controller;
use App\Models\MarketplaceMetaData;
// use App\Models\MarketplaceWebsite;
use Illuminate\Http\Request;

class SemrushController extends Controller
{
    use DataImportTrait;


    // Test Function
    public function testSemrush()
    {
        // $key = MarketplaceMetaData::where('key', 'one')->first();

        // // return $key->value;

        // $key->value = 'jsdjjsdfjf';

        // return $key->save();


        return $this->getSemrushToken('df');

        // return MarketplaceMetaData::where('key', 'one')->update(['value' => 'lovely']);


        // return MarketplaceMetaData::create(['key' => 'new', 'value'=> 'love']);
    }


    /*****************************************
     * SEMRUSH ACCESS TOKEN
     *****************************************
     * The token expires after sometime
     * but the logic to refresh is in extension
     * we need to handle that.
     *
     * 7 days token expiry
    ******************************************/
    private $semrushAccessToken = 'Yb8AcExkFT5CExRaBLKmGePIXaGsvQVd3Og2HJ5r';

    // For refreshing
    private $refreshToken = 'YHK6nXokdSSS9mZXGECvT32PJrFSUNgB6qMUgLLE'; //for refresh
    private $clientSecret = 'd41d8cd98f00b204e9800998ecf8427e'; //for refresh





    /*****************************************
     * SEMRUSH ACCOUNT AUTH API
     *****************************************
     *
     * Require login and access token
     * but works with free account (seoquake)
     *
     * Tested: 10 domains request for summ+links
     *
     * @param string $domain
     * @return array $data
     *
     * search_organic
     * backlinks->data
     *  -> domains (number of reff domains)
     *  -> ascore
    *****************************************/
    public function semrushAuthApi(string $domain)
    {

        $domain = $this->getDomainFromUrl($domain);
        $apiBase = 'https://oauth.semrush.com/api/v1';


        $apiSummary = $apiBase .
                        '/ta/v2/summary?target=' .
                        $domain .
                        '&target_type=domain&access_token=' .
                        $this->semrushAccessToken;

        $apiBacklinks = $apiBase .
                        '/backlinks?target_type=domain&target=' .
                        $domain .
                        '&access_token=' .
                        $this->semrushAccessToken;

        $data['summary'] = json_decode($this->curlRequest($apiSummary), true);
        $data['backlinks'] = json_decode($this->curlRequest($apiBacklinks), true);

        return $data;

    }




    /*****************************************
     * SEMRUSH PUBLIC API DATA
     *****************************************
     *
     * Seoquake extension public api
     * data seems outdated and inconsistent
     *
     * @param string $domain
     * @return array $data|false
     * (summary + links)
    *****************************************/
    public function publicSemrushApi(string $domain)
    {
        // urls
        $publicApi = ['summary' => 'https://seoquake.publicapi.semrush.com/info.php?url=',
            'backlinks' => 'https://bl.publicapi.semrush.com/?url=',
        ];

        $domain = $this->getDomainFromUrl($domain);


        // get data for all apis for single domain
        foreach ($publicApi as $type => $api) {

            // combine api with domain
            $apiURL = $api . $domain;

            // get data or return false
            $request = $this->curlRequest($apiURL);
            if (! $request) {
                return false;
            }

            // convert xml to array
            $xml = simplexml_load_string($request, 'SimpleXMLElement', LIBXML_NOCDATA);
            $json = json_encode($xml);
            $data[$type] = json_decode($json, true);
        }


        // return false if no data exist for domain
        if ($data['summary']['status'] !== 'ok') {
            return false;
        }

        return $data;
    }



    // *****************************************
    // For formating but in process as needed
    public function formatSemrushPublicApiData(array $data): void
    {
        $response['traffic'] = $data['traffic'];
        $response['traffic_cost'] = $data['costs'];
        $response['semrush_rank'] = $data['rank'];
    }




    /*****************************************
     * Refresh Token
    ******************************************/




    /*****************************************
     * GET VALUE BY NAME
    ******************************************/
    private function getSemrushToken(string $key = 'semrush_access_token')
    {
        return MarketplaceMetaData::where('key', $key)->select('value')->first();
    }


    /*****************************************
     * UPDATE VALUE BY NAME: ARRAY
    ******************************************/
    private function updateSemrushTokens(array $tokens)
    {
        foreach ($tokens as $key => $value) {
            MarketplaceMetaData::where('key', $key)->update(['value' => $value]);
        }

        return true;
    }


    /*****************************************
     * INITIAL: CREATE SEMRUSH KEYS
    ******************************************/
    private function createSemrushKeys()
    {
        return MarketplaceMetaData::create(
            ['key' => 'semrush_access_token',  'value' => 'Yb8AcExkFT5CExRaBLKmGePIXaGsvQVd3Og2HJ5r'],
            ['key' => 'semrush_refresh_token', 'value' => 'YHK6nXokdSSS9mZXGECvT32PJrFSUNgB6qMUgLLE'],
            ['key' => 'client_secret',         'value' => 'd41d8cd98f00b204e9800998ecf8427e'],
        );
    }

}
