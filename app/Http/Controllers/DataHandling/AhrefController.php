<?php

namespace App\Http\Controllers\DataHandling;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class AhrefController extends Controller
{
    use DataImportTrait;


    /*****************************************
     * Testing Public Ended Function
     *****************************************/
    public function test()
    {
        // $data['ahref_URL'] = $this->ahrefToolbarURLStatsFetch('https://wikihow.com');
        // $data['ahref_keyword'] = $this->ahrefToolbarKeywordStatsFetch('wikihow');
        // $data['ahref_via_searcheye'] = $this->ahrefViaSearchEye('chatgpt.com');

        return isset($data) ? $data : 'false';
    }



    /*****************************************
     * AHREF COOKIE + AUTHORISATION TOKEN
     *****************************************
     * This is obtained from ahref request
     * from toolbar.
     *
     * Probably stays the same until you
     * re-login from extension.
     *
     * contains cookie: + authroization:
     * words in value.
     *****************************************/
    private $ahrefCookie = 'cookie: BSSESSID=....';

    private $ahrefAuthorization = 'authorization: Bearer .....';






    /*****************************************
     * AHREF URL+DOMAIN STATS VIA SEO TOOLBAR
     *****************************************
     *
     * Using Ahref SEO Toolbar API to get
     * data for url + domain.
     *
     * @param $domain string
     * @return $response | false
     *****************************************/
    public function ahrefToolbarURLStatsFetch(string $url)
    {
        $api = 'https://ahrefs.com/v4/tbGetHeaderV3?input={"target":"' . $url . '"}';

        $request['headers'] = $this->returnAhrefHeaders();
        $request['array'] = true; //convert json

        $data = $this->curlRequest($api, $request); //fetch

        return $this->validateReturnAhrefData($data); //validate
    }





    /*****************************************
     * AHREF KEYWORDS STATS VIA SEO TOOLBAR
     *****************************************
     *
     * Using Ahref SEO Toolbar API to get
     * data for url + domain.
     *
     * @param $keyword string
     * @return $response | false
     *****************************************/
    public function ahrefToolbarKeywordStatsFetch(string $keyword)
    {
        $keyword = urlencode($keyword); //for spaces
        $api = 'https://ahrefs.com/v4/tbGetKeywordStatsV3?input={"keyword":"' . $keyword . '"}';

        $request['headers'] = $this->returnAhrefHeaders();
        $request['array'] = true; //convert json

        $data = $this->curlRequest($api, $request); //fetch

        return $this->validateReturnAhrefData($data); //validate
    }




    /*****************************************
     * SEARCHEYE AHREF PIGGYBACK
     *****************************************
     *
     * Using searcheye as intermediteray
     * for free access
     *
     * @param $domain
     * @return $data
     *****************************************/
    public function ahrefViaSearchEye(string $domain)
    {
        $domain = $this->getDomainFromUrl($domain);

        $api = 'https://searcheye.io/wp-admin/admin-ajax.php';

        $headers = [
            'content-type: application/x-www-form-urlencoded',
            'origin: https://searcheye.io',
            'referer: https://searcheye.io/publishers/',
            'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.0.0 Safari/537.36',
        ];

        $request['headers'] = $headers;
        $request['method'] = 'POST';
        $request['post_fields'] = 'action=check_ahrefs_api&website_url=' . $domain;
        $request['array'] = true; //json decode to array

        return $this->curlRequest($api, $request);
    }





    /*****************************************
     * CREATE AHREF COMPLETE HEADER
     *****************************************/
    private function returnAhrefHeaders()
    {
        return [
            'accept: */*',
            'accept-language: en-US,en-GB;q=0.9,en;q=0.8',
            $this->ahrefAuthorization,
            'cache-control: no-cache',
            'content-type: application/json;charset=utf-8',
            $this->ahrefCookie,
            'dnt: 1',
            'pragma: no-cache',
            'priority: u=1, i',
            'sec-fetch-dest: empty',
            'sec-fetch-mode: cors',
            'sec-fetch-site: none',
            'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36',
        ];
    }



    /*****************************************
     * VALIDATE AHREF RETURNED THE OK RESPONSE
     *****************************************/
    private function validateReturnAhrefData($data)
    {
        // if curl failed return false
        if ($data === false) {
            return false;
        }

        // check ahref returned real data
        if ($data[0] === 'Ok') {
            return $data;
        }
        return false;

    }
}
