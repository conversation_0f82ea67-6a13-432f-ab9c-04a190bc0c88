<?php

$curl = curl_init();

curl_setopt_array($curl, [
    CURLOPT_URL => 'https://chromeuxreport.googleapis.com/v1/records:queryRecord?key=AIzaSyAOmNrsNVnhQP42-jq6yqTx_IOCC30VYM0',
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_ENCODING => '',
    CURLOPT_MAXREDIRS => 10,
    CURLOPT_TIMEOUT => 0,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
    CURLOPT_CUSTOMREQUEST => 'POST',
    CURLOPT_POSTFIELDS => '{"url":"https://androidauthorithy.com/","metrics":["largest_contentful_paint","cumulative_layout_shift","first_input_delay"]}',
    CURLOPT_HTTPHEADER => [
        'authority: chromeuxreport.googleapis.com',
        'accept: */*',
        'accept-language: en-US,en-GB;q=0.9,en;q=0.8',
        'cache-control: no-cache',
        'content-type: application/json;charset=utf-8',
        'dnt: 1',
        'origin: chrome-extension://hgmoccdbjhknikckedaaebbpdeebhiei',
        'pragma: no-cache',
        'sec-fetch-dest: empty',
        'sec-fetch-mode: cors',
        'sec-fetch-site: none',
        'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'x-client-data: CIm2yQEIorbJAQipncoBCIz6ygEIkqHLAQiGoM0BCLnKzQEI0MvNAQiSz80BGPfJzQE=',
    ],
]);

$response = curl_exec($curl);

curl_close($curl);
echo $response;
