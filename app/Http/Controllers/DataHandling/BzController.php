<?php

namespace App\Http\Controllers\DataHandling;

ini_set('max_execution_time', 100000);

use App\Http\Controllers\Controller;
use App\Models\MarketplaceAdminWebsite;
use App\Models\MarketplaceWebsite;
use App\Models\MarketplaceWebsiteCountry;
use App\Models\MarketplaceWebsiteLanguage;
use App\Models\MarketplaceWebsiteSeoStat;
use App\Models\Topic;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

class BzController extends Controller
{
    //import common functionality
    use DataImportTrait;


    public $files = ['file.json'];



    /**********************************************
     * RUN IMPORTER FUNCTION
     ***********************************************/
    public function runBzImport()
    {
        // storage path
        $filesStroagePath = storage_path('app/public/imports/');

        $i = 1;
        $results = [];
        foreach ($this->files as $file) {

            // combine path and file.
            $filePath = $filesStroagePath . $file;

            // process imports
            $results[$i]['imports'] = $this->importBzSites($filePath);

            // store data
            $results[$i]['file'] = $file;

            $i++;
        }

        return $results;
    }




    /**********************************************
     * IMPORT Bz SITES
     *
     * import Bz sites from json file
     *
     * @return $results ARRAY
     **********************************************/
    public function importBzSitesOld($file = '')
    {

        // import file
        // $path = 'app/public/Bz-may24/';
        // $file = storage_path($path."response5-final.json");

        // check file
        if (! file_exists($file)) {
            return 'file doesnt exist';
        }

        // get file data and decode json to array
        $jsonData = file_get_contents($file);
        $data = json_decode($jsonData, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            return 'Invalid JSON data';
        }

        // array variable set
        $results = [];
        $i = 0;

        foreach ($data['results'] as $website) {

            // URL to domain / check domain validity
            $domain = $this->getDomainFromUrl($website['name']);

            // if not valid skip
            if ($domain === false) {
                continue;
            }

            // get callibrated prices for this site
            $pricing = $this->pricingFix($website['price'], $website['niches']);

            // check if already exists
            $domainExists = MarketplaceAdminWebsite::where('website_domain', $domain)->exists();
            if ($domainExists) {
                continue;
            }

            // 1. Marketplace website add
            $marketWebsite = MarketplaceWebsite::create([
                'website_domain' => $domain,
                'domain_registration_date' => $website['domainCreatedAt'],
                'site_requirements' => $this->fixRequirementsText($website['externalNote']),
                'example_post_url' => $this->validateReturnURL($website['publicationSample']),
                'link_relation' => $website['isNoFollow'] ? 'nofollow' : 'dofollow',
                'sponsorship_label' => $website['isRelSponsored'],
                'article_validity_in_months' => $website['lifetimeInMonths'],
                'homepage_visible' => $website['isHomepage'],
                'site_language_id' => $this->findLanguageID($website['language']),

                'guest_post_price' => $pricing['regular'],
                'casino_post_price' => $pricing['casino'],
                'crypto_post_price' => $pricing['crypto'],
                'adult_post_price' => $pricing['adult'],
                'finance_post_price' => $pricing['finance'],
                'cbd_post_price' => $pricing['cbd'],
                'dating_post_price' => $pricing['dating'],
                'site_source' => 'bz',
            ]);



            // 2. Create and attach topics
            $topics = $this->bzCategoriesToTopics($website['category']);
            $attachTopics = $this->attachTopics($marketWebsite, $topics);


            // SEO DATA
            $seoData = [
                'ahref_domain_rank' => $website['domainRating'] ?? 0,
                'ahref_organic_traffic' => $website['organicTraffic'] ?? 0,
                'reffering_domains_count' => $website['referringDomains'] ?? 0,
                'outgoing_links_count' => $website['linkedRootDomains'] ?? 0,

                'semrush_authority_score' => $website['authorityScore'] ?? 0,
                'moz_domain_authority' => $website['domainAuthority'] ?? 0,
                'sistrix_index' => $website['sichtbarkeitsindex'] ?? 0,
                'moz_spam_score' => $website['spamScore'] ?? 0,

                'top_traffic_country_id' => $this->findCountryID($website['primaryCountry']),
                'top_country_traffic' => $website['primaryCountry']['organic_traffic'] ?? 0,
                'secondary_traffic_country_id' => $this->findCountryID($website['secondaryCountry']),
                'secondary_country_traffic' => $website['secondaryCountry']['organic_traffic'] ?? 0,

            ];

            // 3. Save SEO data and update relation
            $seo = new MarketplaceWebsiteSeoStat($seoData);
            $save = $marketWebsite->seoStats()->save($seo);


            // 4. Dsiplay Results
            // $results[$i]['website'] = $marketWebsite;
            // $results[$i]['topics'] = $topics;
            // $results[$i]['attachTopics'] = $attachTopics;
            // $results[$i]['seoSave'] = $save;

            $i++;
        }

        // total count
        $results['totalEntries'] = $i;

        return $results;
    }




    public function importBzSites($file = '')
    {
        if (! file_exists($file)) {
            return 'file doesnt exist';
        }

        $jsonData = file_get_contents($file);
        $data = json_decode($jsonData, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            return 'Invalid JSON data';
        }

        $websites = $data['data']['marketplace']['data'] ?? [];
        $results = [];
        $i = 0;

        foreach ($websites as $website) {
            $domain = $this->getDomainFromUrl($website['name'] ?? null);
            if (! $domain) {
                continue;
            }

            $pricing = $this->pricingFix($website['salesPrice'] ?? 0, $website['linkTypePricing'] ?? []);

            if (MarketplaceAdminWebsite::where('website_domain', $domain)->exists()) {
                continue;
            }

            $marketWebsite = MarketplaceWebsite::create([
                'website_domain' => $domain,
                'domain_registration_date' => $website['registrationDate'] ?? null,
                'site_requirements' => $this->fixRequirementsText($website['externalNotes'] ?? ''),
                'example_post_url' => $this->validateReturnURL($website['samplePublication'] ?? ''),
                'link_relation' => $website['noFollow'] ? 'nofollow' : 'dofollow',
                'sponsorship_label' => $website['isRelSponsored'] ?? false,
                'article_validity_in_months' => $website['lifetimeInMonths'] ?? null,
                'homepage_visible' => $website['isHomepage'] ?? false,
                'site_language_id' => $this->findLanguageID($website['language'] ?? ''),

                'guest_post_price' => $pricing['regular'],
                'casino_post_price' => $pricing['casino'],
                'crypto_post_price' => $pricing['crypto'],
                'adult_post_price' => $pricing['adult'],
                'finance_post_price' => $pricing['finance'],
                'cbd_post_price' => $pricing['cbd'],
                'dating_post_price' => $pricing['dating'],
                'site_source' => 'bz',
            ]);

            // Attach Topics
            $topics = $this->bzCategoriesToTopics($website['categories'] ?? []);
            $this->attachTopics($marketWebsite, $topics);

            // SEO Stats
            $metrics = $website['metrics'] ?? [];
            $seoData = [
                'ahref_domain_rank' => $metrics['ahrefsDomainRating'] ?? 0,
                'ahref_organic_traffic' => $metrics['ahrefsOrganicTraffic'] ?? 0,
                'reffering_domains_count' => $metrics['ahrefsReferringDomains'] ?? 0,
                'outgoing_links_count' => $metrics['ahrefsLinkedRootDomains'] ?? 0,

                'semrush_authority_score' => $metrics['semrushAuthorityScore'] ?? 0,
                'moz_domain_authority' => $metrics['mozDomainAuthority'] ?? 0,
                'sistrix_index' => $metrics['sistrixSichtbarkeitsindex'] ?? 0,
                'moz_spam_score' => $metrics['mozSpamScore'] ?? 0,

                'top_traffic_country_id' => $this->findCountryID($website['primaryCountry']['country'] ?? []),
                'top_country_traffic' => $website['primaryCountry']['organicTraffic'] ?? 0,
                'secondary_traffic_country_id' => $this->findCountryID($website['secondaryCountry']['country'] ?? []),
                'secondary_country_traffic' => $website['secondaryCountry']['organicTraffic'] ?? 0,
            ];

            $seo = new MarketplaceWebsiteSeoStat($seoData);
            $marketWebsite->seoStats()->save($seo);

            $i++;
        }

        $results['totalEntries'] = $i;
        return $results;
    }

    /**********************************************
     * PRICING FIX
     *
     * @param $price INT
     * @param $niches ARRAY
     **********************************************/
    public function pricingFix($price, $niches)
    {

        // negative multiplier
        $adjustmentMultiplier = 1.8;

        // calculate
        $adjustedPrice = round($price / $adjustmentMultiplier);


        // multiply and set
        $pricing['regular'] = $adjustedPrice;

        $pricing['casino'] = isset($niches['casino']['priceMultiplier']) ?
            $niches['casino']['priceMultiplier'] * $adjustedPrice : 0;

        $pricing['crypto'] = isset($niches['crypto']['priceMultiplier']) ?
            $niches['crypto']['priceMultiplier'] * $adjustedPrice : 0;

        $pricing['adult'] = isset($niches['erotic']['priceMultiplier']) ?
            $niches['erotic']['priceMultiplier'] * $adjustedPrice : 0;

        $pricing['finance'] = isset($niches['loan']['priceMultiplier']) ?
            $niches['loan']['priceMultiplier'] * $adjustedPrice : 0;

        $pricing['cbd'] = isset($niches['cbd']['priceMultiplier']) ?
            $niches['cbd']['priceMultiplier'] * $adjustedPrice : 0;

        $pricing['dating'] = isset($niches['dating']['priceMultiplier']) ?
            $niches['dating']['priceMultiplier'] * $adjustedPrice : 0;

        return $pricing;
    }




    /**********************************************
     * Fix Requirements Text
     * Cached for one hour
     *
     * @param $requirements language:bz (code)
     * @return $requirements string
     *
     **********************************************/
    public function fixRequirementsText($text)
    {

        $search = [
            'The subject of the article must match the media',
            'The topic of the article must match the website',
            'DF',
            'media',
            'tag',
        ];

        $replace = [
            'Content Should be relevant to our site',
            'Content Should be relevant to our site',
            'Dofollow',
            'website',
            'label',
        ];

        $fixedText = str_replace($search, $replace, $text);

        return Str::limit($fixedText, 2000);
    }



    /**********************************************
     * FIND LANGUAGE ID
     * Cached for one hour
     *
     * @param $language language:bz (code)
     * @return $id
     *
     **********************************************/
    public function findLanguageID($languageCode)
    {

        $cacheName = 'lang_' . $languageCode;

        // db search: cached
        return Cache::remember($cacheName, 3600, function () use ($languageCode) {
            $language = MarketplaceWebsiteLanguage::where('code', $languageCode)->get();
            return isset($language[0]) ? $language[0]->id : 0;
        });
    }




    /**********************************************
     * FIND COUNTRY ID
     * Cached for one hour
     *
     * @param $countryData primaryCountry:bz
     * @return $id
     *
     **********************************************/
    public function findCountryID($countryData)
    {

        if (! isset($countryData['code'])) {
            return 0;
        }

        $cacheName = 'country_' . $countryData['code'];

        // db search: cached
        return Cache::remember($cacheName, 3600, function () use ($countryData) {

            $country = MarketplaceWebsiteCountry::where('name', $countryData['name'])
                ->orWhere('code', $countryData['code'])
                ->get();

            return isset($country[0]) ? $country[0]->id : 0;
        });
    }




    /***********************************************
     * Bz json categories to single word arrays
     * used for topics generation
     *
     * @param   $data       ARRAY ($data['category'])
     * @return  $topicList  ARRAY
     **/
    public function bzCategoriesToTopics($data)
    {


        // words to remove (spaces included)
        $filterWords = ['and ', '& ', '/', ','];

        // Topic list to return
        $topicList = [];


        // loop over all category names
        foreach ($data as $category) {

            //lower case
            $topic = strtolower($category['name']);

            //remove filter words
            $topic = str_replace($filterWords, '', $topic);

            //multi word to array
            $topic_array = explode(' ', $topic);

            //flatten array
            $topicList = array_merge($topicList, $topic_array);
        }

        // unique words only
        $topicList = array_unique($topicList);


        // remove array items which only contain whitespace
        $topicList = array_filter($topicList, fn($value) => trim($value) !== '');

        // reset keys
        return array_values($topicList);
    }



    /**********************************************
     * Create & Attach Topics
     *
     * Create and attach topics, if exist just attach
     *
     * @param $topics Array
     * @param $website MarketplaceWebsite
     *
     * @return results
     **/
    public function attachTopics(MarketplaceWebsite $website, $topics)
    {

        $topic_ids = [];

        // Create or get ids of topics
        foreach ($topics as $topic) {
            $topic_ids[] = Topic::firstOrCreate(['name' => trim($topic)])->id;
        }

        // Attach topics
        return $website->topics()->sync($topic_ids);
    }







    /**********************************************
     * Testing Bz Files
     **********************************************/
    public function BzTest()
    {

        $file = storage_path('app/public/Bz-may24/response5-final.json');

        if (! file_exists($file)) {
            return 'file doesnt exist';
        }

        // get file data and decode json to array
        $jsonData = file_get_contents($file);
        $data = json_decode($jsonData, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            return 'Invalid JSON data';
        }

        // array get
        $results = [];
        $i = 0;

        foreach ($data['results'] as $website) {
            $results[$i]['website_domain'] = $website['name'];
            $results[$i]['domain_registration_date'] = $website['domainCreatedAt'];
            $results[$i]['site_requirements'] = $this->fixRequirementsText($website['externalNote']);
            $results[$i]['example_post_url'] = $website['publicationSample'];
            $results[$i]['link_relation'] = $website['isNoFollow'] ? 'nofollow' : 'dofollow';
            $results[$i]['sponsorship_label'] = $website['isRelSponsored'];
            $results[$i]['article_validity_in_months'] = $website['lifetimeInMonths'];

            $results[$i]['homepage_visible'] = $website['isHomepage'];
            $results[$i]['site_language_id'] = $this->findLanguageID($website['language']); //id

            // pricing
            $results[$i]['guest_post_price'] = $pricing['regular'];
            $results[$i]['casino_post_price'] = $pricing['casino'];
            $results[$i]['crypto_post_price'] = $pricing['crypto'];
            $results[$i]['adult_post_price'] = $pricing['adult'];
            $results[$i]['finance_post_price'] = $pricing['finance'];
            $results[$i]['cbd_post_price'] = $pricing['cbd'];
            $results[$i]['dating_post_price'] = $pricing['dating'];


            // SEO Table
            $results[$i]['ahref_domain_rank'] = $website['domainRating'];
            $results[$i]['ahref_organic_traffic'] = $website['organicTraffic'];
            $results[$i]['reffering_domains_count'] = $website['referringDomains'];
            $results[$i]['outgoing_links_count'] = $website['linkedRootDomains'];

            $results[$i]['top_traffic_country_id'] = $this->findCountryID($website['primaryCountry']); //id
            $results[$i]['top_country_traffic'] = $website['primaryCountry']['organic_traffic'] ?? 0;

            $results[$i]['secondary_traffic_country_id'] = $this->findCountryID($website['secondaryCountry']);
            $results[$i]['secondary_country_traffic'] = $website['secondaryCountry']['organic_traffic'] ?? 0;

            $results[$i]['semrush_authorithy_score'] = $website['authorityScore'];
            $results[$i]['moz_domain_authority'] = $website['domainAuthority'];
            $results[$i]['sistrix_index'] = $website['sichtbarkeitsindex'];
            $results[$i]['moz_spam_score'] = $website['spamScore'];


            $results[$i]['topics'] = $this->bzCategoriesToTopics($website['category']);
        }

        $i++;

        return $results;
    }
}
