<?php






/*****************************************
// Inactive, using Livewire for admin at the moment
*****************************************/

namespace App\Http\Controllers;

use App\Models\MarketplaceOrder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class MarketplaceAdminController extends Controller
{
    /*********************************************************************
     * GENERATE INVOICE FOR ANY ORDER
     *********************************************************************
     *
     * Generate Invoice For Any Order
     *
     * No security, should only be accessible
     * via safe, secure middleware.
     *
     * @param \App\Models\MarketplaceOrder $order
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     *
     *********************************************************************/
    public function adminInvoice(MarketplaceOrder $order, Request $request)
    {

        // check if team member
        $user = Auth::user();
        if ($user->isTeamMember() === false) {
            abort(404);
        }

        // invoice
        return view('app.invoices.invoice', ['order' => $order, 'user' => $order->user]);

    }


}
