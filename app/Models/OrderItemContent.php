<?php

namespace App\Models;

use App\Traits\HasMediaAttachment;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class OrderItemContent extends Model
{
    use HasMediaAttachment;
    use LogsActivity;

    /**
     *
     * @property int $id
     * @property string $content_source
     * @property string $title
     * @property string $content_body
     * @property string $content_url
     * @property array $files_array
     * @property int $order_item_id
     * @property int $writer_id
     * @property string $comments
     * @property string $advertiser_revision_reason
     * @property string $publisher_advertiser_revision_reason
     * @property Carbon $created_at
     * @property Carbon $updated_at
     *
     * @property-read MarketplaceSingleOrderItem $orderItem
     * @property-read User $writer
     * @property-read Collection|Media $media
     */

    // TABLE
    // -------------------------------------------------------------------------------- //
    protected $table = 'order_item_contents';

    // FILLABLE
    // -------------------------------------------------------------------------------- //
    protected $fillable = [
        'content_source',
        'title',
        'content_body',
        'content_url',
        'files_array',
        'order_item_id',
        'writer_id',
        'comments',
        'advertiser_revision_reason',
        'publisher_advertiser_revision_reason',
    ];

    // CASTS
    // -------------------------------------------------------------------------------- //
    protected $casts = [
        'files_array' => 'array',
    ];

    // APPENDS
    // -------------------------------------------------------------------------------- //
    protected $appends = [
        'created_at_formatted',
        'updated_at_formatted',
    ];

    // RELATIONS
    // -------------------------------------------------------------------------------- //

    public function orderItem()
    {
        return $this->belongsTo(MarketplaceSingleOrderItem::class, 'order_item_id', 'id');
    }

    public function media()
    {
        return $this->morphMany(Media::class, 'mediable');
    }

    public function writer()
    {
        return $this->belongsTo(User::class, 'writer_id');
    }

    /**********************************
     * Attributes
     ***********************************/
    public function getCreatedAtFormattedAttribute()
    {
        return Carbon::parse($this->created_at)->format('d M, Y');
    }

    public function getUpdatedAtFormattedAttribute()
    {
        return Carbon::parse($this->updated_at)->format('d M, Y');
    }
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logAll()
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }
}
