<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

// use App\Models\User;
// use App\Models\MarketplaceWebsite;


class MarketplaceCartItem extends Model
{
    use HasFactory;

    /**
     *
     * @property int $id
     * @property int $user_id
     * @property int $marketplace_website_id
     * @property bool $content_writing
     * @property string $niche
     * @property int $quantity
     * @property Carbon $created_at
     * @property Carbon $updated_at
     *
     * @property-read User $user
     * @property-read MarketplaceWebsite $website
     */

    protected $fillable = [
        'user_id',
        'marketplace_website_id',
        'quantity',
        'niche',
        'content_writing',
    ];

    protected $table = 'cart_items';

    protected $with = ['website'];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function website(): BelongsTo
    {
        return $this->belongsTo(MarketplaceWebsite::class, 'marketplace_website_id');
    }
}
