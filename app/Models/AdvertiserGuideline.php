<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AdvertiserGuideline extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'guidelines_for_writer',
        'guidelines_for_publisher',
    ];

    /**********************************************************************
     * USER RELATIONSHIP
     **********************************************************************
     *
     * A guideline belongs to a user with advertiser role.
     *
     **********************************************************************/
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
