<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class MarketplaceWebsiteLanguage extends Model
{
    use HasFactory;

    /**
     *
     * @property int $id
     * @property string $code
     * @property string $name
     *
     * @property-read Collection|MarketplaceWebsite $websites
     */

    protected $fillable = [
        'code',
        'name',
    ];
    public $timestamps = false;

    public function websites(): HasMany
    {
        return $this->hasMany(MarketplaceWebsite::class, 'site_language_id');
    }
}
