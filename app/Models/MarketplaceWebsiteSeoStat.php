<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

// use App\Models\MarketplaceWebsite;



class MarketplaceWebsiteSeoStat extends Model
{
    use HasFactory;

    /**
     *
     * @property int $id
     * @property int $marketplace_website_id
     * @property int $top_traffic_country_id
     * @property Carbon $created_at
     * @property Carbon $updated_at
     *
     * @property-read MarketplaceWebsite $marketplaceWebsite
     * @property-read MarketplaceWebsiteCountry $country
     */

    protected $guarded = [];

    protected $with = ['country'];


    public function marketplaceWebsite(): BelongsTo
    {
        return $this->belongsTo(MarketplaceWebsite::class, 'marketplace_website_id');
    }

    public function country(): BelongsTo
    {
        return $this->belongsTo(MarketplaceWebsiteCountry::class, 'top_traffic_country_id');
    }
}
