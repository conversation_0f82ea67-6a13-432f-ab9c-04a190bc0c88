<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Carbon;

// use App\Models\MarketplaceOrder;
// use App\Models\User;


class MarketplacePayment extends Model
{
    use HasFactory;

    /**
     *
     * @property int $id
     * @property int $user_id
     * @property float $amount
     * @property string $status
     *
     * @property-read User $user
     * @property-read MarketplaceOrder $order
     */

    // Table Name
    // -------------------------------------------------------------------------------- //
    protected $table = 'order_payments';

    // Guarded
    // -------------------------------------------------------------------------------- //
    protected $guarded = [];

    // APPENDS
    // -------------------------------------------------------------------------------- //
    protected $appends = ['created_at_formatted', 'updated_at_formatted'];

    // RELATIONS
    // -------------------------------------------------------------------------------- //

    /*********************************************************************
     * ORDER
     *********************************************************************
     *
     * Get the order for the payment.
     *
     *********************************************************************/

    public function order(): HasOne
    {
        return $this->hasOne(MarketplaceOrder::class, 'payment_id', 'id');
    }

    /*********************************************************************
     * USER
     *********************************************************************
     *
     * Get the user for the order.
     *
     *********************************************************************/
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /*********************************************************************
     * CHECKOUT SESSION
     *********************************************************************
     *
     * Get the checkout session for the order.
     *
     *********************************************************************/
    public function checkoutSession(): HasOne
    {
        return $this->hasOne(MarketplaceCheckoutSession::class, 'stripe_payment_intent', 'external_transaction_id');
    }


    // Attributes
    // -------------------------------------------------------------------------------- //

    /*********************************************************************
     * CREATED AT FORMATTED
     *********************************************************************
     *
     * Get the created at formatted attribute.
     *
     *********************************************************************/

    public function getCreatedAtFormattedAttribute()
    {
        return Carbon::parse($this->created_at)->format('d M, Y');
    }

    /*********************************************************************
     * UPDATED AT FORMATTED
     *********************************************************************
     *
     * Get the updated at formatted attribute.
     *
     *********************************************************************/
    public function getUpdatedAtFormattedAttribute()
    {
        return Carbon::parse($this->updated_at)->format('d M, Y');
    }
}
