<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class OrderItemPublication extends Model
{
    use LogsActivity;

    /**
     *
     * @property int $id
     * @property int $order_item_id
     * @property int $user_id
     * @property int $marketplace_website_id
     * @property string $publication_url
     * @property string $link_type
     * @property bool $active
     * @property bool $indexed
     * @property Carbon $created_at
     * @property Carbon $updated_at
     *
     * @property-read MarketplaceSingleOrderItem $orderItem
     * @property-read User $user
     * @property-read MarketplaceWebsite $marketplaceWebsite
     */

    // TABLE
    // -------------------------------------------------------------------------------- //
    protected $table = 'order_item_publications';

    // FILLABLE
    // -------------------------------------------------------------------------------- //
    protected $fillable = [
        'order_item_id',
        'user_id',
        'marketplace_website_id',
        'publication_url',
        'advertiser_revision_reason',
        'link_type',
        'active',
        'indexed',
    ];

    // CASTS
    // -------------------------------------------------------------------------------- //
    protected $casts = [
        'active' => 'boolean',
        'indexed' => 'boolean',
    ];

    // RELATIONS
    // -------------------------------------------------------------------------------- //

    public function orderItem()
    {
        return $this->belongsTo(MarketplaceSingleOrderItem::class, 'order_item_id', 'id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function marketplaceWebsite()
    {
        return $this->belongsTo(MarketplaceWebsite::class, 'marketplace_website_id', 'id');
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logAll()
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }
}
