<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Keyword extends Model
{
    use HasFactory;

    /**
     *
     * @property int $id
     * @property string $name
     * @property int|null $volume
     * @property float|null $cpc
     * @property Carbon $created_at
     * @property Carbon $updated_at
     *
     * @property-read Collection|MarketplaceWebsite[] $websites
     */

    protected $guarded = [];

    // Table Name
    // -------------------------------------------------------------------------------- //
    protected $table = 'serp_keywords';

    public function websites(): BelongsToMany
    {
        return $this->belongsToMany(MarketplaceWebsite::class, 'keyword_website', 'keyword_id', 'website_id');
    }
}
