<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class MarketplaceWebsiteCountry extends Model
{
    use HasFactory;

    /**
     *
     * @property int $id
     * @property string $code
     * @property string $name
     *
     */

    protected $fillable = [
        'code',
        'name',
    ];

    public $timestamps = false;

    protected $table = 'countries_list';

    // public function websites(): HasMany
    // {
    //     return $this->hasMany(MarketplaceWebsite::class, 'top_country_id');
    // }

}
