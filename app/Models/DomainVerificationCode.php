<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DomainVerificationCode extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'domain',
        'email',
        'verification_code',
        'expires_at',
        'verified',
        'verified_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'expires_at' => 'datetime',
        'verified_at' => 'datetime',
        'verified' => 'boolean',
    ];

    /**
     * Check if the verification code has expired.
     *
     * @return bool
     */
    public function isExpired(): bool
    {
        return $this->expires_at->isPast();
    }

    /**
     * Check if the verification code is valid (not expired and not verified).
     *
     * @return bool
     */
    public function isValid(): bool
    {
        return ! $this->isExpired() && ! $this->verified;
    }

    /**
     * Mark the verification code as verified.
     *
     * @return bool
     */
    public function markAsVerified(): bool
    {
        return $this->update([
            'verified' => true,
            'verified_at' => now(),
        ]);
    }

    /**
     * Generate a new verification code.
     *
     * @param string $domain
     * @param string $email
     * @return self
     */
    public static function generate(string $domain, string $email): self
    {
        // Delete any existing unverified codes for this domain and email
        self::where('domain', $domain)
            ->delete();

        return self::create([
            'domain' => $domain,
            'email' => $email,
            'verification_code' => str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT),
            'expires_at' => now()->addHours(24), // Expires in 24 hours
        ]);
    }

    /**
     * Find a valid verification code by domain, email, and code.
     *
     * @param string $domain
     * @param string $email
     * @param string $code
     * @return self|null
     */
    public static function findValid(string $domain, string $email, string $code): ?self
    {
        return self::where('domain', $domain)
            ->where('email', $email)
            ->where('verification_code', $code)
            ->where('verified', false)
            ->where('expires_at', '>', now())
            ->first();
    }
}
