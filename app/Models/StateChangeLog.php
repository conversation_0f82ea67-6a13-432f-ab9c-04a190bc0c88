<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

class StateChangeLog extends Model
{
    /**
     *
     * @property int $id
     * @property string $model_type
     * @property int $model_id
     * @property string $from_state
     * @property string $to_state
     * @property Carbon $created_at
     * @property Carbon $updated_at
     *
     * @property-read Model $model
     * @property-read User $user
     */

    // TABLE
    // -------------------------------------------------------------------------------- //
    public $table = 'order_item_state_transitions';

    // FILLABLE
    // -------------------------------------------------------------------------------- //
    protected $fillable = [
        'model_id',
        'model_type',
        'from_state',
        'to_state',
        'user_id',
        'details',
        'ip_address',
        'user_agent',
    ];

    // CASTS
    // -------------------------------------------------------------------------------- //
    protected $casts = [
        'details' => 'array',
    ];

    // RELATIONS
    // -------------------------------------------------------------------------------- //

    public function model()
    {
        return $this->morphTo();
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
