<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class Media extends Model
{
    /**
     *
     * @property int $id
     * @property string $name
     * @property string $path
     * @property string $mime_type
     * @property int $size
     * @property string $disk
     * @property int $user_id
     * @property string $mediable_type
     * @property int $mediable_id
     *
     * @property-read User $user
     * @property-read Model $mediable
     */

    protected $fillable = [
        'name',
        'path',
        'mime_type',
        'size',
        'disk',
        'user_id',
        'mediable_type',
        'mediable_id',
        'meta',
    ];

    protected $casts = [
        'meta' => 'array',
    ];

    public function mediable()
    {
        return $this->morphTo();
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function getUrlAttribute()
    {
        return Storage::disk($this->disk)->url($this->path);
    }
}
