<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class OrderItemRequirement extends Model
{
    use LogsActivity;

    /**
     *
     * @property int $id
     * @property int $order_item_id
     * @property string $article_topic
     * @property string $anchor_text
     * @property string $advertiser_url
     * @property string $requirement_comments
     * @property Carbon $created_at
     * @property Carbon $updated_at
     *
     * @property-read MarketplaceSingleOrderItem $orderItem
     */

    protected $fillable = [
        'order_item_id',
        'article_topic',
        'anchor_text',
        'advertiser_url',
        'requirement_comments',
    ];

    protected $table = 'order_item_requirements';

    public function orderItem()
    {
        return $this->belongsTo(MarketplaceSingleOrderItem::class, 'order_item_id', 'id');
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logAll()
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }
}
