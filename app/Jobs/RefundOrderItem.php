<?php

namespace App\Jobs;

use App\Models\MarketplaceSingleOrderItem;
use App\States\OrderItem\RefundedToWallet;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class RefundOrderItem implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    protected MarketplaceSingleOrderItem $model;


    /*********************************************************************
     * CONSTRUCTOR - INITIALIZE ORDER ITEM REFUND JOB
     *********************************************************************
     *
     * Creates a new job instance for processing order item refunds.
     * Stores the order item model for processing.
     *
     * @param MarketplaceSingleOrderItem $model
     * The order item to be refunded
     *
     *********************************************************************/
    public function __construct(MarketplaceSingleOrderItem $model)
    {
        $this->model = $model;
    }


    /*********************************************************************
     * HANDLE - PROCESS ORDER ITEM REFUND
     *********************************************************************
     *
     * Executes the refund process for an order item:
     * - Transitions order item state to RefundedToWallet
     * - Transfers refund amount to user's wallet
     * - Logs any errors during the process
     *
     * @return bool
     * Returns true on successful refund, false on failure
     *
     *********************************************************************/
    public function handle()
    {
        try {
            // -----------------------
            // Initialize Variables
            $orderItem = $this->model;
            $adminUser = super_admin_user(); //‼️‼️‼️
            $refundAmount = $orderItem->price_paid * 100; // Convert to cents

            // -----------------------
            // Update Order Item State
            $orderItem->state->transitionTo(RefundedToWallet::class);

            // -----------------------
            // Process Wallet Transfer
            $adminUser->wallet->transfer($orderItem->order->user->wallet, $refundAmount, [
                'order_id' => $orderItem->order->id,
                'reference' => 'Order Item Payment Refund To User Wallet',
                'type' => 'order_item_payment_refund',
                'message' => 'Order item payment refunded to user wallet',
            ]);

            return true;
        } catch (\Exception $e) {
            // -----------------------
            // Error Handling
            Log::error('Error refunding order item: ' . $e->getMessage());
            return false;
        }
    }
}
