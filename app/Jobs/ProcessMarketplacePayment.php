<?php

namespace App\Jobs;

use Domain\Order\Order as OrderService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ProcessMarketplacePayment implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    protected $paymentIntent;
    protected $userId;
    protected $isTest;


    /*********************************************************************
     * CONSTRUCTOR - INITIALIZE PAYMENT PROCESSING JOB
     *********************************************************************
     *
     * Creates a new job instance for processing marketplace payments.
     * Initializes payment intent, user ID, and test mode flag.
     *
     * @param string $paymentIntent - Stripe payment intent ID
     * @param int $userId - ID of the user making the payment
     * @param bool $isTest - Flag to indicate if this is a test payment
     *
     *********************************************************************/
    public function __construct(
        string $paymentIntent,
        int $userId,
        bool $isTest = false,
    ) {
        $this->paymentIntent = $paymentIntent;
        $this->userId = $userId;
        $this->isTest = $isTest;
    }


    /*********************************************************************
     * HANDLE - PROCESS MARKETPLACE PAYMENT
     *********************************************************************
     *
     * Main method to process marketplace payment and create order.
     * - Verifies payment status with Stripe
     * - Creates order and order items
     * - Updates checkout session
     * - Cleans up cart items
     * - Sends order notification
     *
     * @return void
     *
     *********************************************************************/
    public function handle(OrderService $order): void
    {
        $order->create([
            'payment_intent_id' => $this->paymentIntent,
            'user_id' => $this->userId,
            'is_test' => $this->isTest,
        ]);
    }
}
