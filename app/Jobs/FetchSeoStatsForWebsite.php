<?php

namespace App\Jobs;

use Domain\Seo\Enums\SEOSourceType;
use Domain\Seo\Jobs\FetchWebsiteSeo;
use App\Models\MarketplaceWebsite;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

/*********************************************************************
 * FETCH SEO STATS JOB - Handles SEO statistics creation for websites
 *********************************************************************
 *
 * Job class responsible for creating SEO statistics for marketplace
 * ..websites if they don't already exist.
 *
 * - Creates SEO stats record if none exists
 * - Uses queue system for background processing
 * - Implements ShouldQueue interface for async processing
 *
 * @param MarketplaceWebsite $website
 * The website model instance to create SEO stats for
 *
 * @return void
 * No return value as this is a job class
 *
 *********************************************************************/
class FetchSeoStatsForWebsite implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(public MarketplaceWebsite $website)
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // -----------------------
        // Check and Create SEO Stats
        // Creates SEO statistics record if none exists for the website
        if (! $this->website->seoStats) {
            $this->website->seoStats()->create();

            // Fetch SEO Stats for all sources
            foreach (SEOSourceType::cases() as $sourceType) {
                FetchWebsiteSeo::dispatch($this->website, $sourceType->value, 'domain')
                    ->delay(now()->addSeconds(10));
            }
        }
    }
}
