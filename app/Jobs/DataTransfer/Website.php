<?php

namespace App\Jobs\DataTransfer;

use App\Models\MarketplaceWebsite;
use App\Models\MarketplaceWebsiteSeoStat;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

class Website implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    public function __construct(private int $batchSize, private int $offset) {}

    public function handle()
    {
        // -----------------------
        // Get the websites from the old database
        // -----------------------

        $columns_old_db = [
            'website_domain',
            'site_language_id',
            'main_category_id',
            'category_global_rank',
            'site_title',
            'site_description',
            'domain_registration_date',
            'guest_post_price',
            'link_insert_price',
            'casino_post_price',
            'adult_post_price',
            'finance_post_price',
            'dating_post_price',
            'cbd_post_price',
            'crypto_post_price',
            'site_requirements',
            'example_post_url',
            'article_validity_in_months',
            'indexed_article',
            'link_relation',
            'sponsorship_label',
            'homepage_visible',
            'publisher_user_id',
            'contact_email',
            'active',
            'site_source',
            'internal_note',
        ];

        $websites = MarketplaceWebsite::on('old_db')
            ->select($columns_old_db)
            ->withoutGlobalScopes()
            ->offset($this->offset)
            ->limit($this->batchSize)
            ->get();

        $websiteSeoStats = MarketplaceWebsiteSeoStat::on('old_db')
            ->withoutGlobalScopes()
            ->without('country')
            ->offset($this->offset)
            ->limit($this->batchSize)
            ->get();




        // -----------------------
        // Start transaction for actual data transfer
        // -----------------------
        DB::beginTransaction();
        try {
            // -----------------------
            // Transfer the websites to the new database
            // -----------------------
            foreach ($websites as $key => $website) {
                $websiteData = $website->toArray();

                if ($websiteData['publisher_user_id'] === 0) {
                    $websiteData['publisher_user_id'] = super_admin_user()->id;
                }

                $newWebsite = MarketplaceWebsite::firstOrCreate($websiteData);


                // Update the SEO stats
                $seoStatData = $websiteSeoStats[$key]->toArray();
                unset($seoStatData['marketplace_website_id']);
                unset($seoStatData['id']);

                // Fix foreign key constraint issues by converting 0 values to null
                if (isset($seoStatData['top_traffic_country_id']) && $seoStatData['top_traffic_country_id'] === 0) {
                    $seoStatData['top_traffic_country_id'] = null;
                }

                if (isset($seoStatData['secondary_traffic_country_id']) && $seoStatData['secondary_traffic_country_id'] === 0) {
                    $seoStatData['secondary_traffic_country_id'] = null;
                }

                MarketplaceWebsiteSeoStat::updateOrCreate(
                    ['marketplace_website_id' => $newWebsite->id],
                    $seoStatData,
                );
            }


            // -----------------------
            // Commit the transaction
            // -----------------------
            DB::commit();

            // -----------------------
            // Return the result
            // -----------------------
            return [
                'success' => true,
                'websites_count' => $websites->count(),
                'message' => 'Successfully transferred ' . $websites->count() . ' websites.',
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
}
