<?php

namespace App\Enums;

/***************************************************************************
 * ENUMS: CHILD ORDER ITEM STATES
 ***************************************************************************
 * Story: Each order item progresses through multiple stages and may
 * move back and forth before being marked as completed.
 * For example: providing requirements, writing, publishing, and revision.
 *
 * Purpose: These states are used to manage the states of child order items.
 *
 * Usage: They are part of the state machine that controls the flow and
 * applies logic-based rules.
****************************************************************************/
enum OrderItemStates: string
{
    case RequirementsPending = 'requirements-pending';
    case RequirementAwaitingPublisherApproval = 'requirement-awaiting-publisher-approval';
    case RequirementRevisionRequested = 'requirement-revision-requested';
    case ContentPending = 'content-pending';
    case ContentAssignedToWriter = 'content-assigned-to-writer';
    case ContentAdvertiserReview = 'content-advertiser-review';
    case ContentRevisionRequestedByAdvertiser = 'content-revision-requested-by-advertiser';
    case ContentAwaitingPublisherApproval = 'content-awaiting-publisher-approval';
    case PublicationInProcess = 'publication-in-process';
    case PublicationDelivered = 'publication-delivered';
    case OrderItemCompleted = 'order-item-completed';
    case OrderItemCancelled = 'order-item-cancelled';
    case OrderItemRefundRequested = 'order-item-refund-requested';
    case PublicationRevisionRequestedByAdvertiser = 'publication-revision-requested-by-advertiser';
    case RefundedToWallet = 'refunded-to-wallet';
}
