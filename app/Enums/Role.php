<?php

namespace App\Enums;

/***************************************************************************
 * ENUMS: USER ROLES
 ***************************************************************************
* Story: Defines the types of user roles on our site, each with
 * different capabilities.
 *
 * Purpose: Stored in the users table to identify the role of each user.
****************************************************************************/
enum Role: string
{
    //Backend Team
    case SuperAdmin = 'superadmin';
    case Admin = 'admin';
    case Sales = 'sales';
    case Finance = 'finance';
    case Outreach = 'outreach';
    case Writer = 'writer';

    // Customer
    case Advertiser = 'advertiser';

    // Service Provider
    case Publisher = 'publisher';
}
