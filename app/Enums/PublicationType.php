<?php

namespace App\Enums;

/***************************************************************************
 * ENUMS: PUBLICATION TYPE
 ***************************************************************************
 * Story: Articles can be published as regular posts, press releases, news,
 * or other types. These differ in how they are published and the value
 * and use cases they provide.
 *
 * Purpose: These enums define the publication types used in our system.
****************************************************************************/
enum PublicationType: string
{
    case Article = 'article';
    case PressRelease = 'press-release';
    case News = 'news';
}
