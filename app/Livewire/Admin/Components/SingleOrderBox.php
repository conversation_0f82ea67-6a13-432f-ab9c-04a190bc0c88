<?php

namespace App\Livewire\Admin\Components;

use App\Models\MarketplaceSingleOrderItem;
use App\Notifications\orderItemDelivered;
use App\Notifications\orderNeedAttention;
// Mails
// use Illuminate\Support\Facades\Mail;
// use App\Mail\OrderItemDelivered;
// use App\Mail\OrderNeedAttention;


use Illuminate\Support\Facades\Notification;
use Livewire\Attributes\Validate;
use Livewire\Component;

class SingleOrderBox extends Component
{
    public MarketplaceSingleOrderItem $item;
    public $orderStates;


    /******************************
     * FORM FIELDS
    /******************************/
    #[Validate('url')]
    public $content_url = '';

    // [Validate('url')]

    public $content_writer = '';
    public $order_status = '';
    public $internal_note = '';
    public $publisher_email = '';
    public $publisher_payment_paid = '';
    public $payment_proof = '';



    /******************************
     * Mounting form data for
     * previous inputs fillign
     ******************************/
    public function mount(): void
    {
        $this->content_url = $this->item->content_url;
        $this->order_status = $this->item->state;
        $this->publisher_email = $this->item->website->contact_email ?? '';
        $this->publisher_payment_paid = $this->item->publisher_payment_paid;
        $this->content_writer = $this->item->content_writer;
        $this->internal_note = $this->item->internal_note;
        $this->payment_proof = $this->item->payment_proof;


        $this->orderStates = MarketplaceSingleOrderItem::getStates();
    }




    /******************************
    //  UPDATE ORDER DATA
     ******************************/
    public function save(): void
    {
        $this->validate();

        $this->item->update([
            'content_url' => $this->content_url,
            'state' => $this->order_status,
            'publisher_payment_paid' => $this->publisher_payment_paid,
            'content_writer' => $this->content_writer,
            'internal_note' => $this->internal_note,
            'payment_proof' => $this->payment_proof,
        ]);


        $this->js("toast('Updated', {type: 'success', position: 'bottom-center'})");
    }





    /******************************
    // Notify Customer
     ******************************/
    public function notifyCustomer($type): void
    {

        if ($type === 'needs-attention') {
            Notification::send($this->item->order->user, new orderNeedAttention($this->item->order));
            $this->js("toast('Email Sent', {type: 'success', position: 'bottom-center'})");
        }

        if ($type === 'item-delivered') {
            Notification::send($this->item->order->user, new orderItemDelivered($this->item));
            $this->js("toast('Email Sent', {type: 'success', position: 'bottom-center'})");
        }
    }



    /******************************
     * RENDER
     ******************************/
    public function render()
    {
        return view('livewire.admin.components.single-order-box');
    }
}
