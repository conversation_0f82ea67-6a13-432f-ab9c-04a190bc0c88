<?php

namespace App\Livewire\Advertiser;

use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class Wallet extends Component
{
    public function mount(): void {}

    public function getBalanceProperty(): int
    {
        return Auth::user()->user_balance;
    }

    public function getTransactionsProperty()
    {
        $user = Auth::user();
        if ($user) {
            return $user->transactions()
                ->with('wallet', 'wallet.holder')
                ->orderBy('id', 'desc')
                ->get()
                ->map(fn($transaction) => [
                    'id' => $transaction->id,
                    'type' => $transaction->type,
                    'user' => $transaction->wallet->holder,
                    'order_id' => $transaction->meta['order_id'] ?? 'N/A',
                    'amount' => $transaction->amount,
                    'reference' => $transaction->meta['reference'] ?? 'N/A',
                    'status' => $transaction->confirmed ? 'Approved' : 'Pending',
                    'date' => $transaction->created_at->format('Y-m-d H:i:s'),
                    'debit' => $transaction->type === 'withdraw' ? number_format($transaction->amount / 100, 2) : '',
                    'credit' => $transaction->type === 'deposit' ? number_format($transaction->amount / 100, 2) : '',
                ]);
        }
    }



    public function render()
    {
        return view('livewire.advertiser.wallet');
    }
}
