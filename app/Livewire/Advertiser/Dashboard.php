<?php

namespace App\Livewire\Advertiser;

use App\Enums\OrderItemStates;
use App\Models\MarketplaceOrder;
use App\Models\MarketplaceSingleOrderItem;
use Domain\Order\Modules\OrdersTraits;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithoutUrlPagination;
use Livewire\WithPagination;

class Dashboard extends Component
{
    use OrdersTraits;
    use WithoutUrlPagination;
    use WithPagination;
    public $search = '';


    public function mount(): void {}

    public function getOrdersProperty()
    {
        return $this->getOrderListWithSearch($this->search);
    }


    public function getTotalSpentProperty(): int
    {
        return MarketplaceOrder::sum('price_paid');
    }

    public function getTotalRefundsProperty(): int
    {
        return MarketplaceSingleOrderItem::where('state', OrderItemStates::RefundedToWallet->value)->sum('price_paid');
    }

    public function getWalletBalanceProperty(): int
    {
        return Auth::user()->user_balance;
    }

    public function getInProgressProperty(): int
    {
        return MarketplaceOrder::orderInProgress()->count();
    }

    public function getLateProperty(): int
    {
        return MarketplaceOrder::orderLate()->count();
    }

    public function getCompletedProperty(): int
    {
        return MarketplaceOrder::orderCompleted()->count();
    }

    public function getNeedAttentionProperty(): int
    {
        return MarketplaceOrder::orderPending()->count();
    }

    public function render()
    {
        return view('livewire.advertiser.dashboard');
    }
}
