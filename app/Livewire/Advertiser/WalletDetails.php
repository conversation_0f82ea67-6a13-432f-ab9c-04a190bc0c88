<?php

namespace App\Livewire\Advertiser;

use Bavix\Wallet\Models\Transaction;
use Livewire\Component;

class WalletDetails extends Component
{
    public $id;

    public function mount($id): void
    {
        $this->id = $id;
    }

    public function getTransactionProperty()
    {
        return Transaction::find($this->id);
    }



    public function render()
    {
        return view('livewire.advertiser.wallet-details');
    }
}
