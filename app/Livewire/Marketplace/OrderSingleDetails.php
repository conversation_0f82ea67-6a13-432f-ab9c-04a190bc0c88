<?php

namespace App\Livewire\Marketplace;

use App\Models\MarketplaceOrder;
use Domain\Order\Modules\OrdersTraits;
use Livewire\Component;

class OrderSingleDetails extends Component
{
    use OrdersTraits;
    public MarketplaceOrder $order;
    public $order_memo;

    public function mount($id): void
    {
        $this->order = $this->getOrderById($id, ['orderItems', 'orderItems.website', 'orderItems.website.publisher', 'orderItems.publication', 'user', 'payment.checkoutSession']);
        $this->order_memo = $this->order->payment->checkoutSession->order_memo ?? 'No Note';
    }


    public function render()
    {
        return view('livewire.marketplace.order-single-details');
    }
}
