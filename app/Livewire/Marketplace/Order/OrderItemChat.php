<?php

namespace App\Livewire\Marketplace\Order;

use App\Models\MarketplaceSingleOrderItem;
use App\Services\MessageService;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithFileUploads;

class OrderItemChat extends Component
{
    use WithFileUploads;

    public MarketplaceSingleOrderItem $item;
    public $message = '';
    public $attachment = null;
    public $messages = [];
    public $orderItem;
    public $newMessage = '';

    protected $messageService;

    public function boot(MessageService $messageService): void
    {
        $this->messageService = $messageService;
    }

    public function mount(): void
    {
        $this->orderItem = MarketplaceSingleOrderItem::find($this->item->id);
        $this->loadMessages();
    }

    public function loadMessages(): void
    {
        $messages = $this->messageService->getMessages($this->orderItem);
        $this->messages = $messages->load(['sender', 'receiver'])->toArray();
    }

    public function getListeners()
    {
        return [
            "echo-private:order-item.{$this->item->id},NewMessageSent" => 'handleNewMessage',
            "echo-private:order-item.{$this->item->id},MessageReadAtUpdated" => 'handleMessageReadAtUpdated',
        ];
    }

    public function handleNewMessage($event): void
    {
        $newMessage = $event['message'];
        $this->messages[] = [
            'text' => $newMessage['text'],
            'sender_id' => $newMessage['sender_id'],
            'receiver_id' => $newMessage['receiver_id'],
            'created_at' => $newMessage['created_at'],
            'is_read' => $newMessage['is_read'],
            'attachment_path' => $newMessage['attachment_path'] ?? null,
            'sender' => $newMessage['sender'] ?? null,
        ];
        $this->dispatch('scroll-to-bottom');
    }

    public function handleMessageReadAtUpdated($event): void
    {
        $messageId = $event['message_id'];
        $readAt = $event['read_at'];

        foreach ($this->messages as &$message) {
            if ($message['id'] === $messageId) {
                $message['read_at'] = $readAt;
                $message['is_read'] = true;
                break;
            }
        }
    }

    public function sendMessage(): void
    {
        $this->validate([
            'message' => 'required_without:attachment|string|max:1000',
            'attachment' => 'nullable|file|max:2048|mimes:jpg,jpeg,png,gif,webp,doc,docx,pdf,txt',
        ]);

        $messageData = [
            'text' => $this->message,
            'order_item_id' => $this->orderItem->id,
            'sender_id' => Auth::id(),
            'receiver_id' => $this->orderItem->website->publisher->id,
        ];

        $message = $this->messageService->storeMessage($messageData, $this->attachment);
        $message->load(['sender', 'receiver']);

        $this->messages[] = [
            'id' => $message->id,
            'text' => $message->text,
            'sender_id' => $message->sender_id,
            'receiver_id' => $message->receiver_id,
            'created_at' => $message->created_at,
            'attachment_path' => $message->attachment_path,
            'sender' => [
                'id' => $message->sender->id,
                'name' => $message->sender->name,
                'profile_photo_path' => $message->sender->profile_photo_path,
            ],
            'receiver' => [
                'id' => $message->receiver->id,
                'name' => $message->receiver->name,
                'profile_photo_path' => $message->receiver->profile_photo_path,
            ],
        ];

        $this->message = '';
        $this->attachment = null;

        $this->dispatch('scroll-to-bottom');
    }

    public function render()
    {
        return view('livewire.marketplace.order.order-item-chat');
    }
}
