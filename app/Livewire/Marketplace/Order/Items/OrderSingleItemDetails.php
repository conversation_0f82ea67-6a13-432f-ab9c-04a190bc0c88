<?php

namespace App\Livewire\Marketplace\Order\Items;

use App\Models\MarketplaceSingleOrderItem;
use Domain\Order\Modules\OrdersTraits;
use Livewire\Component;

class OrderSingleItemDetails extends Component
{
    use OrdersTraits;
    public MarketplaceSingleOrderItem $orderItem;
    public $timeline;


    public function mount($id): void
    {
        // -----------------------
        // Get Order Item
        $this->orderItem = $this->getOrderItemById($id, ['website.publisher']);

        // -----------------------
        // Get Order Item Timeline
        $this->timeline = $this->getOrderItemTimeline($this->orderItem);
    }





    public function render()
    {
        return view('livewire.marketplace.order.items.order-single-item-details');
    }
}
