<?php

namespace App\Livewire\Marketplace;

use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class NotificationsPanel extends Component
{
    public $notifications;
    public $unreadCount;

    public $user;

    public function getListeners()
    {
        return [
            'notification-marked-read' => '$refresh',
            'echo-private:App.Models.User.' . Auth::id() . ',notification' => '$refresh',
        ];
    }

    public function mount(): void
    {
        $this->user = Auth::user();
        $this->loadNotifications();
    }

    public function loadNotifications(): void
    {
        $this->notifications = $this->user->notifications()->latest()->get();
        $this->unreadCount = $this->notifications->where('read_at', null)->count();
        $this->dispatch('update-bell-count', count: $this->unreadCount);
    }

    public function markAsRead($id): void
    {
        $this->user->notifications()->findOrFail($id)->markAsRead();
        $this->loadNotifications();
        $this->dispatch('notification-marked-read');
    }

    public function markAllAsRead(): void
    {
        $this->user->unreadNotifications->markAsRead();
        $this->loadNotifications();
        $this->dispatch('notification-marked-read');
        $this->dispatch('update-bell-count', count: $this->unreadCount);
    }

    public function render()
    {
        return view('livewire.marketplace.notifications-panel');
    }
}
