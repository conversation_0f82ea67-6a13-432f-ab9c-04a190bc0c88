<?php

namespace App\Traits\Filters;

use Illuminate\Database\Eloquent\Builder;

/*********************************************************************
 * DATE FILTER TRAIT
 **********************************************************************
 *
 * Dynamic and reusable date filtering functionality for any feature
 * across the application. Supports preset ranges, custom date ranges,
 * and status-based date column mapping.
 *
 * USAGE EXAMPLES:
 * - Basic: $this->applyDateFilter($query, $filters)
 * - Custom column: $this->applyDateFilter($query, $filters, 'updated_at')
 * - Custom keys: $this->applyDateFilter($query, $filters, 'created_at', 'date_range', 'from_date', 'to_date')
 * - Status-based: $this->applyDateFilter($query, $filters, $this->getDateColumnForStatus('status'))
 *
 *********************************************************************/
trait DateFilterTrait
{
    /*********************************************************************
     * APPLY DATE FILTER
     **********************************************************************
     *
     * Apply date filter to query based on preset or custom range.
     * Supports dynamic date column and custom filter keys for maximum
     * flexibility across different features.
     *
     * @param Builder $query      - The query builder instance to filter
     * @param array   $filters    - Filter array containing date parameters
     * @param string  $dateColumn - Database column to filter on (default: 'created_at')
     * @param string  $rangeKey   - Key for preset range in filters (default: 'preset_range')
     * @param string  $startKey   - Key for start date in filters (default: 'start_date')
     * @param string  $endKey     - Key for end date in filters (default: 'end_date')
     * @return Builder - Filtered query builder
     *
     *********************************************************************/
    protected function applyDateFilter(
        Builder $query,
        array $filters,
        string $dateColumn = 'created_at',
        string $rangeKey = 'preset_range',
        string $startKey = 'start_date',
        string $endKey = 'end_date',
    ): Builder {
        // -----------------------
        // Skip If No Range
        if (empty($filters[$rangeKey]) || $filters[$rangeKey] === 'show_all') {
            return $query;
        }


        // -----------------------
        // Handle Custom Date Range
        if ($filters[$rangeKey] === 'custom') {
            return $this->applyCustomDateRange($query, $filters, $dateColumn, $startKey, $endKey);
        }


        // -----------------------
        // Handle Preset Date Ranges
        return $this->applyPresetDateRange($query, $filters[$rangeKey], $dateColumn);
    }




    /*********************************************************************
     * APPLY PRESET DATE RANGE
     **********************************************************************
     *
     * Apply predefined date ranges to query. Supports common ranges
     * like today, yesterday, last 7/30/90 days, and last 12 months.
     *
     * @param Builder $query       - The query builder instance to filter
     * @param string  $presetRange - Preset range identifier (today, yesterday, etc.)
     * @param string  $dateColumn  - Database column to filter on
     * @return Builder - Filtered query builder
     *
     *********************************************************************/
    private function applyPresetDateRange(Builder $query, string $presetRange, string $dateColumn): Builder
    {
        return match ($presetRange) {
            'today' => $query->whereDate($dateColumn, today()),
            'yesterday' => $query->whereDate($dateColumn, today()->subDay()),
            'last_7_days' => $query->whereBetween($dateColumn, [now()->subDays(6)->startOfDay(), now()->endOfDay()]),
            'last_30_days' => $query->whereBetween($dateColumn, [now()->subDays(29)->startOfDay(), now()->endOfDay()]),
            'last_90_days' => $query->whereBetween($dateColumn, [now()->subDays(89)->startOfDay(), now()->endOfDay()]),
            'last_12_months' => $query->whereBetween($dateColumn, [now()->subMonths(12)->startOfDay(), now()->endOfDay()]),
            default => $query,
        };
    }




    /*********************************************************************
     * APPLY CUSTOM DATE RANGE
     **********************************************************************
     *
     * Apply custom start/end date filters to query. Handles both
     * start and end dates independently, allowing partial ranges.
     *
     * @param Builder $query      - The query builder instance to filter
     * @param array   $filters    - Filter array containing date values
     * @param string  $dateColumn - Database column to filter on
     * @param string  $startKey   - Key for start date in filters (default: 'start_date')
     * @param string  $endKey     - Key for end date in filters (default: 'end_date')
     * @return Builder - Filtered query builder
     *
     *********************************************************************/
    private function applyCustomDateRange(
        Builder $query,
        array $filters,
        string $dateColumn,
        string $startKey = 'start_date',
        string $endKey = 'end_date',
    ): Builder {
        // -----------------------
        // Apply Start Date Filter
        if (! empty($filters[$startKey])) {
            $query->whereDate($dateColumn, '>=', $filters[$startKey]);
        }


        // -----------------------
        // Apply End Date Filter
        if (! empty($filters[$endKey])) {
            $query->whereDate($dateColumn, '<=', $filters[$endKey]);
        }

        return $query;
    }
}
