<?php

namespace App\Traits;

use App\Models\Media;

trait HasMediaAttachment
{
    public function attachMedia(array $mediaIds): void
    {
        $validIds = array_filter($mediaIds, fn($id) => is_numeric($id));

        if (empty($validIds)) {
            return;
        }

        // -----------------------
        // Remove existing media
        // -----------------------
        Media::where('mediable_type', static::class)
            ->where('mediable_id', $this->id)
            ->update([
                'mediable_type' => null,
                'mediable_id' => null,
            ]);


        // -----------------------
        // Attach new media
        // -----------------------
        Media::whereIn('id', $validIds)->update([
            'mediable_type' => static::class,
            'mediable_id' => $this->id,
        ]);
    }
}
