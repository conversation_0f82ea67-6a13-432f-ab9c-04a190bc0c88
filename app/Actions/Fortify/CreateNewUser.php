<?php

namespace App\Actions\Fortify;

use App\Models\User;
use EragLaravelDisposableEmail\Rules\DisposableEmailRule;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use <PERSON><PERSON>\Fortify\Contracts\CreatesNewUsers;
use <PERSON><PERSON>\Jetstream\Jetstream;

/****************************************************
 * CREATE NEW USER (LARAVEL FORTIFY DEFAULT CLASS)
/****************************************************/
class CreateNewUser implements CreatesNewUsers
{
    use PasswordValidationRules;

    /**
     * Validate and create a newly registered user.
     *
     * @param  array<string, string>  $input
     */
    public function create(array $input): User
    {

        Validator::make($input, [
            'name' => ['required', 'string', 'max:255'],
            'email' => [
                'required',
                'string',
                'email',
                'max:255',
                'unique:users',
                new DisposableEmailRule(),
            ],
            'password' => $this->passwordRules(),
            'terms' => Jetstream::hasTermsAndPrivacyPolicyFeature() ? ['accepted', 'required'] : '',
        ])->validate();

        return User::create([
            'name' => $input['name'],
            'email' => $input['email'],
            'password' => Hash::make($input['password']),
        ]);
    }
}
