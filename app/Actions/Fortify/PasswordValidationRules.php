<?php

namespace App\Actions\Fortify;

use Illuminate\Contracts\Validation\Rule;
use <PERSON><PERSON>\Fortify\Rules\Password;

/*******************************************************
 * PASSWORD VALIDATION (LARAVEL FORTIFY DEFAULT CLASS)
/******************************************************/
trait PasswordValidationRules
{
    /**
     * Get the validation rules used to validate passwords.
     *
     * @return array<int, Rule|array|string>
     */
    protected function passwordRules(): array
    {
        return ['required', 'string', new Password(), 'confirmed'];
    }
}
