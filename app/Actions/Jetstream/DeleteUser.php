<?php

namespace App\Actions\Jetstream;

use App\Models\User;
use <PERSON><PERSON>\Jetstream\Contracts\DeletesUsers;

/*******************************************************
 * DELETE USER (LARAVEL FORTIFY DEFAULT CLASS)
/******************************************************/
class DeleteUser implements DeletesUsers
{
    /**
     * Delete the given user.
     */
    public function delete(User $user): void
    {
        $user->deleteProfilePhoto();
        $user->tokens->each->delete();
        $user->delete();
    }
}
