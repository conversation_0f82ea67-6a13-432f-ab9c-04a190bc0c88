<?php

namespace App\Helpers;

use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Illuminate\Database\Query\Builder;

class BatchJobManager
{
    public function __construct(
        private int $batchSize,
        private string $jobClass,
        private Builder|EloquentBuilder $query,
        private string $queue,
        private ?int $chunkSize = null,
    ) {}

    public function __invoke()
    {
        $totalRecords = $this->query->count();
        $recordsToProcess = $this->chunkSize ?? $totalRecords;

        // Calculate how many jobs we need to dispatch
        $numberOfJobs = (int) ceil($recordsToProcess / $this->batchSize);


        for ($i = 0; $i < $numberOfJobs; $i++) {
            $job = new $this->jobClass($this->batchSize, $i * $this->batchSize);
            dispatch($job)->onQueue($this->queue);
        }

        return $numberOfJobs;
    }
}
