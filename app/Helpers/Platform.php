<?php

use App\Enums\Role;
use App\Models\User;

/**
 * Platform Helper
 *
 * This helper provides platform-specific functionality and configuration.
 */

/*********************************************************************
 * SUPER ADMIN USER - Get cached super admin user instance
 *********************************************************************
 *
 * Retrieves and caches the super admin user for performance.
 * - Checks cache first to avoid database queries
 * - Falls back to database query if not cached
 * - Caches result permanently for future requests
 *
 * @return User|null
 * Returns the super admin user model or null if not found
 *
 *********************************************************************/
if (! function_exists('super_admin_user')) {
    function super_admin_user()
    {
        // Check if super admin user is already cached
        if (cache()->has('super_admin_user')) {
            return cache()->get('super_admin_user');
        }

        // Cache super admin user permanently
        return cache()->rememberForever('super_admin_user', function () {
            return User::where(function ($query): void {
                $query->where('role', Role::SuperAdmin->value)
                    ->where('id', 1)
                    ->where('email', '<EMAIL>');
            })->first();
        });
    }
}


/*********************************************************************
 * PROCESS ADDRESS DATA - Convert address fields to JSON format
 *********************************************************************
 *
 * Extracts address-related fields and converts to JSON storage format.
 * - Collects address, city, and postal_code fields
 * - Stores in address_data JSON column
 * - Removes individual fields from main data array
 *
 * @param array $userData
 * User data containing address fields to be processed
 *
 * @return array
 * Processed user data with JSON address format
 *
 *********************************************************************/
if (! function_exists('processAddressData')) {
    function processAddressData(array $userData): array
    {
        // Extract address fields from user data
        $fields = ['address', 'city', 'postal_code'];
        $userData['address_data'] = array_intersect_key($userData, array_flip($fields));
        return array_diff_key($userData, array_flip($fields));
    }
}
