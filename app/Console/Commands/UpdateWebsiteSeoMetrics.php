<?php

namespace App\Console\Commands;

use App\Jobs\FetchSeoStatsForWebsite;
use App\Models\MarketplaceWebsite;
use Illuminate\Console\Command;

/***********************************************************************************
 * COMMAND: UPDATE WEBSITE SEO METRICS
 ***********************************************************************************
 * Update SEO metrics for all active marketplace websites perodically.
 * Dispatches jobs to fetch SEO stats for each active website in the
 * marketplace based on last updated date.
************************************************************************************/
class UpdateWebsiteSeoMetrics extends Command
{
    /**
     * The name and signature of the console command.
     * This command can be executed using: php artisan website:update-seo-metrics
     *
     * @var string
     */
    protected $signature = 'website:update-seo-data';


    /**
     * The console command description.
     * This description is shown when running php artisan list
     *
     * @var string
     */
    protected $description = 'Update SEO metrics for all active websites';


    /**********************************************************
     * EXECUTE COMMAND
     **********************************************************
     *
     * Main command execution method that processes all active
     * websites.
     *
     * - Retrieves all active marketplace websites
     * - Dispatches a job for each website to fetch its SEO
     *   statistics.
     * - Places the jobs on a dedicated 'seostats' queue
     *
     * @return void
     *
    /***********************************************************/
    public function handle(): void
    {
        // -----------------------
        // Get Active Websites
        $activeWebsites = MarketplaceWebsite::active()->get();


        // -----------------------
        // Dispatch SEO Stats Jobs
        foreach ($activeWebsites as $website) {
            FetchSeoStatsForWebsite::dispatch($website)->onQueue('seostats');
        }
    }
}
