<?php

namespace App\Console\Commands;

use App\Models\MarketplaceCartItem;
use Carbon\Carbon;
use Illuminate\Console\Command;

/*********************************************************************
 * COMMAND: DELETE OLD CART ITEMS (> 60days)
/*********************************************************************/
class DeleteOldCartItems extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cart:delete-old-items';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete cart items that are older than specific number of days. (defined in config)';


    /*********************************************************************
     * EXECUTE COMMAND
     *********************************************************************
     *
     * Main command execution method
     * - Calculates cutoff date (60 days ago)
     * - Deletes matching cart items
     * - Outputs success message
     *
     * @return void - Displays count of deleted items
     *
     *********************************************************************/
    public function handle(): void
    {
        // -----------------------
        // Calculate Cutoff Date
        $cutoffDate = Carbon::now()->subDays(config('pressbear.delete_old_cart_items_days', 60));

        // -----------------------
        // Delete Old Items
        $deletedCount = MarketplaceCartItem::where('created_at', '<', $cutoffDate)
            ->delete();

        // -----------------------
        // Output Results
        $this->info("Successfully deleted {$deletedCount} old cart items.");
    }
}
