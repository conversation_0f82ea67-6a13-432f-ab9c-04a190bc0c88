<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class MessageReadAtUpdated implements ShouldBroadcast
{
    use Dispatchable;
    use InteractsWithSockets;
    use SerializesModels;

    public $messageId;
    public $readAt;

    /*********************************************************************
     * EVENT: MESSAGE READ STATUS UPDATE
     *********************************************************************
     *
     * Creates a new Message Read Status update event instance.
     * This event is broadcasted when a message's read status is updated.
     *
     * @param int $messageId
     * The ID of the message whose read status is being updated
     *
     * @param string $readAt
     * The timestamp when the message was read
     *
     *********************************************************************/
    public function __construct($messageId, $readAt)
    {
        $this->messageId = $messageId;
        $this->readAt = $readAt;
    }





    /*********************************************************************
     * BROADCAST CHANNEL DEFINITION
     *********************************************************************
     *
     * Specifies the private channel for broadcasting the message read status.
     * Uses a private channel specific to the message ID for secure communication.
     *
     * @return \Illuminate\Broadcasting\PrivateChannel
     * Returns a private channel instance for the specific message
     *
     *********************************************************************/
    public function broadcastOn()
    {
        return new PrivateChannel('order-item.' . $this->messageId);
    }





    /*********************************************************************
     * BROADCAST DATA PAYLOAD
     *********************************************************************
     *
     * Defines the data payload to be broadcasted with the event.
     * Includes message ID and read timestamp for client-side processing.
     *
     * @return array
     * Returns an array containing message ID and read timestamp
     *
     *********************************************************************/
    public function broadcastWith()
    {
        return [
            'message_id' => $this->messageId,
            'read_at' => $this->readAt,
        ];
    }
}
