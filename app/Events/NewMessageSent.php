<?php

namespace App\Events;

use App\Models\Chat;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/*********************************************************************
 * NEW MESSAGE SENT EVENT
 **********************************************************************
 *
 * Broadcasts new message to private channel for real-time updates.
 *
 * Handles message broadcasting to specific order item channel.
 * Loads sender and receiver relationships for complete message data.
 *
 * @param Chat $message
 * The message model instance to broadcast
 *
 * @return array
 * Returns array of channels to broadcast on
 *
/*********************************************************************/
class NewMessageSent implements ShouldBroadcast
{
    use Dispatchable;
    use InteractsWithSockets;
    use SerializesModels;

    public $message;

    /*********************************************************************
     * CONSTRUCTOR
     *********************************************************************
     *
     * Initializes event with message instance.
     *
     * @param Chat $message
     * The message model to broadcast
     *
    /*********************************************************************/
    public function __construct(Chat $message)
    {
        $this->message = $message;
    }




    /*********************************************************************
     * BROADCAST ON CHANNEL
     **********************************************************************
     *
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     * Returns array of channels to broadcast on
     *
    /*********************************************************************/
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('order-item.' . $this->message->order_item_id),
        ];
    }



    /*********************************************************************
     * BROADCAST MESSAGE
     **********************************************************************
     *
     * Get the data to broadcast.
     *
     * @return array<string, mixed>
     * Returns array of message data with sender and receiver relationships
     *
    /*********************************************************************/
    public function broadcastWith(): array
    {
        return [
            'message' => $this->message->load(['sender', 'receiver']),
        ];
    }
}
