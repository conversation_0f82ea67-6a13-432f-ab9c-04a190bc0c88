<?php

namespace App\States\OrderItem;

use App\Enums\OrderItemStates;
use App\Jobs\RefundOrderItem;
use App\Notifications\User\UserNotification;
use App\Services\MarketplaceOrderService;

/*********************************************************************
 * ORDER ITEM CANCELLED STATE
 **********************************************************************
 *
 * This state represents an order item that has been cancelled.
 * It handles the validation and transition logic for cancelled orders.
 *
 *********************************************************************/
class OrderItemCancelled extends OrderItemState
{
    public static $name = OrderItemStates::OrderItemCancelled->value;

    /*********************************************************************
     * GET STATE LABEL
     **********************************************************************
     *
     * Returns the human-readable label for this state.
     *
     * @return string The display label for this state
     *
     *********************************************************************/
    public static function label(): string
    {
        return 'Order Cancelled';
    }

    /*********************************************************************
     * HANDLE STATE TRANSITION
     **********************************************************************
     *
     * Handles the state transition logic. Called when transitioning to
     * this state.
     *
     * @param mixed $model The order item model instance
     * @param string $from The previous state
     * @param string $to The new state
     * @return void
     *
     *********************************************************************/
    public static function handle($model, string $from, string $to): void
    {
        $emailData = [];
        $emailData['customerName'] = $model->order->user->name ?? 'N/A';
        $emailData['emailSubject'] = 'Order Cancelled for Order #' . $model->order->id;
        $emailData['statusLabel'] = 'Order Cancelled for Order Item #' . $model->id;
        $emailData['from'] = $from;
        $emailData['to'] = $to;

        // Update the order's final received amount
        self::updateFinalReceivedAmount($model);

        // Refund To Customer
        RefundOrderItem::dispatch($model);

        // Send Notification
        $model->order->user->notify(new UserNotification(
            'Item #' . $model->id . ' Cancelled for Order #' . $model->order->id,
            $model->id,
            $emailData,
            'emails.states.order-item-cancelled',
        ));
    }

    /*********************************************************************
     * GET POSSIBLE TRANSITIONS
     **********************************************************************
     *
     * Returns the list of possible states that can be transitioned to
     * from this state.
     *
     * @return array Array of state classes that are valid transitions
     * from this state
     *
     *********************************************************************/
    public static function possibleTransitions(): array
    {
        return [
            OrderItemRefundRequested::class,
        ];
    }

    /*********************************************************************
     * UPDATE FINAL RECEIVED AMOUNT
     **********************************************************************
     *
     * Updates the order's final received amount when an item is cancelled.
     *
     * @param mixed $model The order item model
     * @return void
     *
     *********************************************************************/
    private static function updateFinalReceivedAmount($model): void
    {
        MarketplaceOrderService::updateFinalReceivedAmount($model->order);
    }
}
