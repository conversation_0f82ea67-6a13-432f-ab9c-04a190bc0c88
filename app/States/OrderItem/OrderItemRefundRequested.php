<?php

namespace App\States\OrderItem;

use App\Enums\OrderItemStates;
use App\Notifications\User\UserNotification;

/*********************************************************************
 * ORDER ITEM REFUND REQUESTED STATE
 **********************************************************************
 *
 * This state represents an order item where a refund has been requested.
 * It handles the validation and transition logic for this specific state.
 *
 *********************************************************************/
class OrderItemRefundRequested extends OrderItemState
{
    public static $name = OrderItemStates::OrderItemRefundRequested->value;

    /*********************************************************************
     * GET STATE LABEL
     **********************************************************************
     *
     * Returns the human-readable label for this state.
     *
     * @return string The display label for this state
     *
     *********************************************************************/
    public static function label(): string
    {
        return 'Order Item Refund Requested';
    }

    /*********************************************************************
     * HANDLE STATE TRANSITION
     **********************************************************************
     *
     * Handles the state transition logic. Called when transitioning to
     * this state. Logs the refund request.
     *
     * @param mixed $model The order item model instance
     * @param string $from The previous state
     * @param string $to The new state
     * @return void
     *
     *********************************************************************/
    public static function handle($model, string $from, string $to): void
    {
        $user = super_admin_user();
        $emailData = [];
        $emailData['customerName'] = $user->name ?? 'N/A';
        $emailData['emailSubject'] = 'Order Item Refund Requested';
        $emailData['statusLabel'] = 'Order Item Refund Requested';
        $emailData['from'] = $from;
        $emailData['to'] = $to;
        $emailData['orderItem'] = $model;
        // Send Notification
        $user->notify(new UserNotification(
            'Item #' . $model->id . ' Order Item Refund Requested',
            $model->id,
            $emailData,
            'emails.states.order-item-refund-requested',
        ));
    }

    /*********************************************************************
     * GET POSSIBLE TRANSITIONS
     **********************************************************************
     *
     * Returns the list of possible states that can be transitioned to
     * from this state.
     *
     * @return array Array of state classes that are valid transitions
     * from this state
     *
     *********************************************************************/
    public static function possibleTransitions(): array
    {
        return [
            RefundedToWallet::class,
        ];
    }
}
