<?php

namespace App\States\OrderItem;

use App\Enums\OrderItemStates;
use App\Notifications\User\UserNotification;

/*********************************************************************
 * PUBLICATION DELIVERED STATE
 **********************************************************************
 *
 * This state represents an order item where the publication has been
 * delivered to the advertiser. It handles the validation and transition
 * logic for this specific state.
 *
 *********************************************************************/
class PublicationDelivered extends OrderItemState
{
    public static $name = OrderItemStates::PublicationDelivered->value;

    /*********************************************************************
     * GET STATE LABEL
     **********************************************************************
     *
     * Returns the human-readable label for this state.
     *
     * @return string The display label for this state
     *
     *********************************************************************/
    public static function label(): string
    {
        return 'Publication Delivered';
    }

    /*********************************************************************
     * HANDLE STATE TRANSITION
     **********************************************************************
     *
     * Handles the state transition logic. Called when transitioning to
     * this state.
     *
     * @param mixed $model The order item model instance
     * @param string $from The previous state
     * @param string $to The new state
     * @return void
     *
     *********************************************************************/
    public static function handle($model, string $from, string $to): void
    {
        $emailData = [];
        $emailData['customerName'] = $model->order->user->name ?? 'N/A';
        $emailData['emailSubject'] = 'Content Published For Order #' . $model->order->id;
        $emailData['statusLabel'] = 'Content Published For Order Item #' . $model->id;
        $emailData['from'] = $from;
        $emailData['to'] = $to;
        $emailData['published_url'] = $model->publication?->publication_url ?? 'N/A';
        // Send Notification
        $model->order->user->notify(new UserNotification(
            'Item #' . $model->id . ' Content Published For Order #' . $model->order->id,
            $model->id,
            $emailData,
            'emails.states.order-item-publication-delivered',
        ));
    }

    /*********************************************************************
     * GET POSSIBLE TRANSITIONS
     **********************************************************************
     *
     * Returns the list of possible states that can be transitioned to
     * from this state.
     *
     * @return array Array of state classes that are valid transitions
     * from this state
     *
     *********************************************************************/
    public static function possibleTransitions(): array
    {
        return [
            OrderItemCompleted::class,
            PublicationRevisionRequestedByAdvertiser::class,
        ];
    }
}
