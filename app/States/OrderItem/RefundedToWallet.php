<?php

namespace App\States\OrderItem;

use App\Enums\OrderItemStates;
use App\Notifications\User\UserNotification;
use App\Services\MarketplaceOrderService;
use Barryvdh\DomPDF\Facade\Pdf;

/*********************************************************************
 * REFUNDED TO WALLET STATE
 **********************************************************************
 *
 * This state represents an order item that has been refunded to the
 * user's wallet. It handles the validation and transition logic for
 * this specific state.
 *
 *********************************************************************/
class RefundedToWallet extends OrderItemState
{
    public static $name = OrderItemStates::RefundedToWallet->value;

    /*********************************************************************
     * GET STATE LABEL
     **********************************************************************
     *
     * Returns the human-readable label for this state.
     *
     * @return string The display label for this state
     *
     *********************************************************************/
    public static function label(): string
    {
        return 'Refunded To Wallet';
    }

    /*********************************************************************
     * HANDLE STATE TRANSITION
     **********************************************************************
     *
     * Handles the state transition logic. Called when transitioning to
     * this state.
     *
     * @param mixed $model The order item model instance
     * @param string $from The previous state
     * @param string $to The new state
     * @return void
     *
     *********************************************************************/
    public static function handle($model, string $from, string $to): void
    {
        $emailData = [];
        $emailData['customerName'] = $model->order->user->name ?? 'N/A';
        $emailData['emailSubject'] = 'Order Item Refunded to Wallet';
        $emailData['statusLabel'] = 'Order Item Refunded to Wallet';
        $emailData['from'] = $from;
        $emailData['to'] = $to;

        // Update the order's final received amount
        self::updateFinalReceivedAmount($model);

        // Generate Credit Note PDF
        $pdf = Pdf::loadView('pdf.credit-note', [
            'orderItem' => $model,
            'user' => $model->order->user,
        ]);

        // Send Notification with Credit Note PDF
        $model->order->user->notify(new UserNotification(
            'Order Item Refunded to Wallet',
            $model->id,
            $emailData,
            'emails.states.order-item-refunded-to-wallet',
            [
                'data' => base64_encode($pdf->output()),
                'name' => 'credit-note-' . $model->id . '.pdf',
            ],
        ));
    }

    /*********************************************************************
     * GET POSSIBLE TRANSITIONS
     **********************************************************************
     *
     * Returns the list of possible states that can be transitioned to
     * from this state.
     *
     * @return array Array of state classes that are valid transitions
     * from this state
     *
     *********************************************************************/
    public static function possibleTransitions(): array
    {
        return [];
    }

    /*********************************************************************
     * UPDATE FINAL RECEIVED AMOUNT
     **********************************************************************
     *
     * Updates the order's final received amount when an item is refunded.
     *
     * @param mixed $model The order item model
     * @return void
     *
     *********************************************************************/
    private static function updateFinalReceivedAmount($model): void
    {
        MarketplaceOrderService::updateFinalReceivedAmount($model->order);
    }
}
