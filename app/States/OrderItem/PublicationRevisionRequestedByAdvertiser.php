<?php

namespace App\States\OrderItem;

use App\Enums\OrderItemStates;
use App\Notifications\User\UserNotification;

/*********************************************************************
 * PUBLICATION REVISION REQUESTED BY ADVERTISER STATE
 **********************************************************************
 *
 * This state represents an order item where the advertiser has requested
 * revisions to the publication. It handles the validation and transition
 * logic for this specific state.
 *
 *********************************************************************/
class PublicationRevisionRequestedByAdvertiser extends OrderItemState
{
    public static $name = OrderItemStates::PublicationRevisionRequestedByAdvertiser->value;

    /*********************************************************************
     * GET STATE LABEL
     **********************************************************************
     *
     * Returns the human-readable label for this state.
     *
     * @return string The display label for this state
     *
     *********************************************************************/
    public static function label(): string
    {
        return 'Publication Revision Requested By Advertiser';
    }

    /*********************************************************************
     * HANDLE STATE TRANSITION
     **********************************************************************
     *
     * Handles the state transition logic. Called when transitioning to
     * this state.
     *
     * @param mixed $model The order item model instance
     * @param string $from The previous state
     * @param string $to The new state
     * @return void
     *
     *********************************************************************/
    public static function handle($model, string $from, string $to): void
    {
        $emailData = [];
        $emailData['customerName'] = $model->website->publisher->name ?? 'N/A';
        $emailData['emailSubject'] = 'Publication Revision Requested By Advertiser For Order #' . $model->order->id;
        $emailData['statusLabel'] = 'Publication Revision Requested By Advertiser For Order Item #' . $model->id;
        $emailData['from'] = $from;
        $emailData['to'] = $to;
        $emailData['revision'] = $model->advertiser_revision_reason;
        // Send Notification
        $model->website->publisher->notify(new UserNotification(
            'Item #' . $model->id . ' Publication Revision Requested By Advertiser For Order #' . $model->order->id,
            $model->id,
            $emailData,
            'emails.states.order-item-publication-revision-requested-by-advertiser',
        ));
    }

    /*********************************************************************
     * GET POSSIBLE TRANSITIONS
     **********************************************************************
     *
     * Returns the list of possible states that can be transitioned to
     * from this state.
     *
     * @return array Array of state classes that are valid transitions
     * from this state
     *
     *********************************************************************/
    public static function possibleTransitions(): array
    {
        return [
            PublicationDelivered::class,
        ];
    }
}
