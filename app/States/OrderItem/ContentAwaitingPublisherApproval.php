<?php

namespace App\States\OrderItem;

use App\Enums\OrderItemStates;
use App\Notifications\User\UserNotification;

/*********************************************************************
 * CONTENT AWAITING PUBLISHER APPROVAL STATE
 **********************************************************************
 *
 * This state represents an order item where content is waiting for
 * publisher approval. It handles the validation and transition logic
 * for this specific state.
 *
 *********************************************************************/
class ContentAwaitingPublisherApproval extends OrderItemState
{
    public static $name = OrderItemStates::ContentAwaitingPublisherApproval->value;

    /*********************************************************************
     * GET STATE LABEL
     **********************************************************************
     *
     * Returns the human-readable label for this state.
     *
     * @return string The display label for this state
     *
     *********************************************************************/
    public static function label(): string
    {
        return 'Content Awaiting Publisher Approval';
    }

    /*********************************************************************
     * HANDLE STATE TRANSITION
     **********************************************************************
     *
     * Handles the state transition logic. Called when transitioning to
     * this state.
     *
     * @param mixed $model The order item model instance
     * @param string $from The previous state
     * @param string $to The new state
     * @return void
     *
     *********************************************************************/
    public static function handle($model, string $from, string $to): void
    {
        $emailData = [];
        $emailData['customerName'] = $model->website->publisher->name ?? 'N/A';
        $emailData['emailSubject'] = 'Content Updated for Order #' . $model->order->id;
        $emailData['statusLabel'] = 'Content Updated for Order Item #' . $model->id;
        $emailData['from'] = $from;
        $emailData['to'] = $to;
        $emailData['content'] = $model->content;

        // Send Notification (includes both database and email)
        $model->website->publisher->notify(new UserNotification(
            'Content Updated for Order Item #' . $model->id,
            $model->id,
            $emailData,
            'emails.states.content-awaiting',
        ));
    }

    /*********************************************************************
     * GET POSSIBLE TRANSITIONS
     **********************************************************************
     *
     * Returns the list of possible states that can be transitioned to
     * from this state.
     *
     * @return array Array of state classes that are valid transitions
     * from this state
     *
     *********************************************************************/
    public static function possibleTransitions(): array
    {
        return [
            PublicationInProcess::class,
            ContentPending::class,
        ];
    }
}
