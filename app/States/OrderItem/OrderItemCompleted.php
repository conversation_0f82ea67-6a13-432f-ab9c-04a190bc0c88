<?php

namespace App\States\OrderItem;

use App\Enums\OrderItemStates;
use App\Notifications\User\UserNotification;
use App\Services\MarketplaceOrderService;
use App\Services\WalletService;

/**
 * Order Item Completed State
 *
 * Represents the final state of an order item that has been successfully completed.
 * This state handles the completion workflow including wallet credits, cost updates,
 * and user notifications.
 *
 * @package App\States\OrderItem
 * <AUTHOR> Team
 */
class OrderItemCompleted extends OrderItemState
{
    /**
     * The state identifier
     */
    public static string $name = OrderItemStates::OrderItemCompleted->value;

    /**
     * Get the human-readable label for this state
     *
     * @return string The display label for this state
     */
    public static function label(): string
    {
        return 'Order Item Completed';
    }

    /**
     * Handle the state transition logic
     *
     * This method is called when transitioning to this state and performs
     * the following actions:
     * - Credits the publisher's wallet
     * - Updates the order's total cost
     * - Updates the order's final received amount
     * - Sends completion notification to the customer
     *
     * @param mixed $model The order item model instance
     * @param string $from The previous state identifier
     * @param string $to The new state identifier
     * @return void
     */
    public static function handle($model, string $from, string $to): void
    {
        // Prepare email notification data
        $emailData = [
            'customerName' => $model->order->user->name ?? 'N/A',
            'emailSubject' => 'Order Item Completed',
            'statusLabel' => 'Order Item Completed',
            'from' => $from,
            'to' => $to,
        ];

        // Credit the publisher's wallet for completed work
        WalletService::creditPublisherWallet($model);

        // Update the order's total cost
        self::updateOrderCost($model);

        // Send completion notification to the customer
        self::sendCompletionNotification($model, $emailData);
    }

    /**
     * Get possible transitions from this state
     *
     * Since this is a final state, no further transitions are allowed.
     *
     * @return array Array of state classes that are valid transitions
     */
    public static function possibleTransitions(): array
    {
        return [];
    }

    /**
     * Update the order's total cost
     *
     * @param mixed $model The order item model
     * @return void
     */
    private static function updateOrderCost($model): void
    {
        $totalCost = $model->publisher_payment_paid + $model->order->our_cost;

        $model->order->update([
            'our_cost' => $totalCost,
        ]);

        // Update the order's final received amount
        self::updateFinalReceivedAmount($model);
    }

    /**
     * Update the order's final received amount
     *
     * @param mixed $model The order item model
     * @return void
     */
    private static function updateFinalReceivedAmount($model): void
    {
        MarketplaceOrderService::updateFinalReceivedAmount($model->order);
    }

    /**
     * Send completion notification to the customer
     *
     * @param mixed $model The order item model
     * @param array $emailData The email notification data
     * @return void
     */
    private static function sendCompletionNotification($model, array $emailData): void
    {
        $notificationTitle = sprintf('Item #%d Order Item Completed', $model->id);

        $model->order->user->notify(new UserNotification(
            $notificationTitle,
            $model->id,
            $emailData,
            'emails.states.order-item-completed',
        ));
    }
}
