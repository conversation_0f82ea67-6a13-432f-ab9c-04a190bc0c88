<?php

namespace App\States\OrderItem;

use App\Enums\OrderItemStates;
use App\Notifications\User\UserNotification;

/*********************************************************************
 * CONTENT REVISION REQUESTED BY ADVERTISER STATE
 **********************************************************************
 *
 * This state represents an order item where the advertiser has requested
 * revisions to the content. It handles the validation and transition
 * logic for this specific state.
 *
 *********************************************************************/
class ContentRevisionRequestedByAdvertiser extends OrderItemState
{
    public static $name = OrderItemStates::ContentRevisionRequestedByAdvertiser->value;

    /*********************************************************************
     * GET STATE LABEL
     **********************************************************************
     *
     * Returns the human-readable label for this state.
     *
     * @return string The display label for this state
     *
     *********************************************************************/
    public static function label(): string
    {
        return 'Content Revision Requested By Advertiser';
    }

    /*********************************************************************
     * HANDLE STATE TRANSITION
     **********************************************************************
     *
     * Handles the state transition logic. Called when transitioning to
     * this state.
     *
     * @param mixed $model The order item model instance
     * @param string $from The previous state
     * @param string $to The new state
     * @return void
     *
     *********************************************************************/
    public static function handle($model, string $from, string $to): void
    {
        $emailData = [];
        $emailData['customerName'] = $model->content->writer->name ?? 'N/A';
        $emailData['emailSubject'] = 'Content Revision Requested for Order #' . $model->order->id;
        $emailData['statusLabel'] = 'Content Revision Requested for Order Item #' . $model->id;
        $emailData['from'] = $from;
        $emailData['to'] = $to;
        $emailData['content_advertiser_revision_reason'] = $model->content->advertiser_revision_reason ?? 'N/A';

        // Send Notification
        $model->content->writer->notify(new UserNotification(
            'Content Revision Requested for Order Item #' . $model->id,
            $model->id,
            $emailData,
            'emails.states.content-revision-requested-by-advertiser',
        ));
    }

    /*********************************************************************
     * GET POSSIBLE TRANSITIONS
     **********************************************************************
     *
     * Returns the list of possible states that can be transitioned to
     * from this state.
     *
     * @return array Array of state classes that are valid transitions
     * from this state
     *
     *********************************************************************/
    public static function possibleTransitions(): array
    {
        return [
            ContentAdvertiserReview::class,
        ];
    }
}
