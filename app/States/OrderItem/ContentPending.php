<?php

namespace App\States\OrderItem;

use App\Enums\OrderItemStates;
use App\Notifications\User\UserNotification;

/*********************************************************************
 * CONTENT PENDING STATE
 **********************************************************************
 *
 * This state represents an order item where content is pending
 * assignment or review. It handles the validation and transition
 * logic for this specific state.
 *
 *********************************************************************/
class ContentPending extends OrderItemState
{
    public static $name = OrderItemStates::ContentPending->value;

    /*********************************************************************
     * GET STATE LABEL
     **********************************************************************
     *
     * Returns the human-readable label for this state.
     *
     * @return string The display label for this state
     *
     *********************************************************************/
    public static function label(): string
    {
        return 'Content Pending';
    }

    /*********************************************************************
     * HANDLE STATE TRANSITION
     **********************************************************************
     *
     * Handles the state transition logic. Called when transitioning to
     * this state.
     *
     * @param mixed $model The order item model instance
     * @param string $from The previous state
     * @param string $to The new state
     * @return void
     *
     *********************************************************************/
    public static function handle($model, string $from, string $to): void
    {
        $emailData = [
            'customerName' => $model->order->user->name ?? 'N/A',
            'emailSubject' => 'Pitch Approved for Order #' . $model->order->id,
            'statusLabel' => 'Pitch Approved for Order Item #' . $model->id,
            'from' => $from,
            'to' => $to,
            'requirements' => $model->requirements,
        ];


        // Send Notification
        $model->order->user->notify(new UserNotification(
            'Pitch Approved for Order Item #' . $model->id,
            $model->id,
            $emailData,
            'emails.states.content-pending',
        ));

        // Send Notification to admin
        if (! $model->is_content_provided_by_customer) {
            super_admin_user()->notify(new UserNotification(
                'Please Assign Content to Writer for Order Item #' . $model->id,
                $model->id,
                $emailData,
                'emails.states.content-assigned-to-writer',
            ));
        }
    }

    /*********************************************************************
     * GET POSSIBLE TRANSITIONS
     **********************************************************************
     *
     * Returns the list of possible states that can be transitioned to
     * from this state.
     *
     * @return array Array of state classes that are valid transitions
     * from this state
     *
     *********************************************************************/
    public static function possibleTransitions(): array
    {
        return [
            ContentAwaitingPublisherApproval::class,
            ContentAssignedToWriter::class,
        ];
    }
}
