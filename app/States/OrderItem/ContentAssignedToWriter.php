<?php

namespace App\States\OrderItem;

use App\Enums\OrderItemStates;
use App\Notifications\User\UserNotification;

/*********************************************************************
 * CONTENT ASSIGNED TO WRITER STATE
 **********************************************************************
 *
 * This state represents an order item where content has been assigned
 * to a writer. It handles the validation and transition logic for this
 * specific state.
 *
 *********************************************************************/
class ContentAssignedToWriter extends OrderItemState
{
    public static $name = OrderItemStates::ContentAssignedToWriter->value;

    /*********************************************************************
     * GET STATE LABEL
     **********************************************************************
     *
     * Returns the human-readable label for this state.
     *
     * @return string The display label for this state
     *
     *********************************************************************/
    public static function label(): string
    {
        return 'Content Assigned To Writer';
    }

    /*********************************************************************
     * HANDLE STATE TRANSITION
     **********************************************************************
     *
     * Handles the state transition logic. Called when transitioning to
     * this state.
     *
     * @param mixed $model The order item model instance
     * @param string $from The previous state
     * @param string $to The new state
     * @return void
     *
     *********************************************************************/
    public static function handle($model, string $from, string $to): void
    {

        $user = $model->content->writer ?? null;
        if (! $user) {
            return;
        }
        $emailData = [];
        $emailData['customerName'] = $user->name ?? 'N/A';
        $emailData['emailSubject'] = 'New Content Assignment - Order #' . $model->order->id;
        $emailData['statusLabel'] = 'Content Assignment - Order Item #' . $model->id;
        $emailData['from'] = $from;
        $emailData['to'] = $to;
        $emailData['requirements'] = $model->requirements;
        $model->save();

        // Send Notification (includes both database and email)
        $user->notify(new UserNotification(
            'New Content Assignment - Order Item #' . $model->id,
            $model->id,
            $emailData,
            'emails.states.content-assigned',
        ));

    }

    /*********************************************************************
     * GET POSSIBLE TRANSITIONS
     **********************************************************************
     *
     * Returns the list of possible states that can be transitioned to
     * from this state.
     *
     * @return array Array of state classes that are valid transitions
     * from this state
     *
     *********************************************************************/
    public static function possibleTransitions(): array
    {
        return [
            ContentAdvertiserReview::class,
        ];
    }
}
