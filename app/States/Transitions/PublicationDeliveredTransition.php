<?php

namespace App\States\Transitions;

use App\Models\MarketplaceSingleOrderItem;
use App\States\OrderItem\PublicationDelivered;
use Illuminate\Validation\ValidationException;
use Spatie\ModelStates\Transition;

class PublicationDeliveredTransition extends Transition
{
    private MarketplaceSingleOrderItem $orderItem;
    private string $message;
    public function __construct(MarketplaceSingleOrderItem $orderItem)
    {
        $this->orderItem = $orderItem;
        $this->message = 'Publication is not delivered. Please deliver the publication URL first.';
    }

    /*********************************************************************
     * VALIDATE
     *********************************************************************/
    public function validate(): void
    {
        if (! $this->orderItem->publication) {
            throw ValidationException::withMessages([
                'message' => [$this->message],
            ]);
        }
    }

    /*********************************************************************
     * HANDLE
     *********************************************************************/
    public function handle()
    {
        // Validate the order item
        $this->validate();

        // Transition to the next state
        $this->orderItem->state = PublicationDelivered::class;
        $this->orderItem->save();

        return $this->orderItem;
    }
}
