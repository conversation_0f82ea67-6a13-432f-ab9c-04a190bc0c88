<?php

namespace App\States\Transitions;

use App\Models\MarketplaceSingleOrderItem;
use App\States\OrderItem\RequirementAwaitingPublisherApproval;
use Illuminate\Validation\ValidationException;
use Spatie\ModelStates\Transition;

class RequirementAwaitingPublisherApprovalTransition extends Transition
{
    private MarketplaceSingleOrderItem $orderItem;
    private string $message;
    public function __construct(MarketplaceSingleOrderItem $orderItem)
    {
        $this->orderItem = $orderItem;
        $this->message = 'Requirements are missing. Please submit requirements first.';
    }

    /*********************************************************************
     * VALIDATE
     *********************************************************************/
    public function validate(): void
    {
        if (! $this->orderItem->requirements) {
            throw ValidationException::withMessages([
                'message' => [$this->message],
            ]);
        }
    }

    /*********************************************************************
     * HANDLE
     *********************************************************************/
    public function handle()
    {
        // Validate the order item
        $this->validate();

        // Transition to the next state
        $this->orderItem->state = RequirementAwaitingPublisherApproval::class;
        $this->orderItem->save();

        return $this->orderItem;
    }
}
