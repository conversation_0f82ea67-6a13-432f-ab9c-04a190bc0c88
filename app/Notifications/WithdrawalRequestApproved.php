<?php

namespace App\Notifications;

use App\Models\WalletWithdrawalRequest;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class WithdrawalRequestApproved extends Notification implements ShouldQueue
{
    use Queueable;

    protected $withdrawal;

    public function __construct(WalletWithdrawalRequest $withdrawal)
    {
        $this->withdrawal = $withdrawal;
    }

    public function via($notifiable): array
    {
        return ['mail'];
    }

    public function toMail($notifiable): MailMessage
    {
        return (new MailMessage())
            ->subject('Withdrawal Request Approved')
            ->line('Your withdrawal request has been approved.')
            ->line('Amount: $' . number_format($this->withdrawal->amount, 2))
            ->line('User: ' . $this->withdrawal->user->name)
            ->line('Payment Method: ' . ucfirst($this->withdrawal->paymentMethod->key))
            ->line('Thank you for using our platform.');
    }



    public function toArray($notifiable): array
    {
        return [
            'withdrawal_id' => $this->withdrawal->id,
            'user_id' => $this->withdrawal->user_id,
            'amount' => $this->withdrawal->amount,
            'payment_method' => ucfirst($this->withdrawal->paymentMethod->key),
        ];
    }
}
