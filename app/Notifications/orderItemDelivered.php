<?php

namespace App\Notifications;

use App\Mail\OrderItemDeliveredMail;
use Illuminate\Bus\Queueable;
// use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Notifications\Notification; //mail class

class orderItemDelivered extends Notification implements ShouldQueue
{
    use Queueable;

    public $orderItem;

    /*************************************************
     * Order Data
    /************************************************/
    public function __construct($orderItemData)
    {
        $this->orderItem = $orderItemData;
    }


    /*************************************************
     * Notification Types
     *
     * @return array<int, string>
     **************************************************/
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }


    /*************************************************
     * Mailable Class
     **************************************************/
    public function toMail(object $notifiable): Mailable
    {
        return (new OrderItemDeliveredMail($this->orderItem))
            ->to($notifiable->email);
    }


    /***************************************************
     * TO DB FOR Ui.
     ****************************************************/
    public function toDatabase(object $notifiable): array
    {
        return [
            'title' => 'Item Delivered',
            'message' => 'One of your order item is delivered. Review on the order page. #order id: ' .
                $this->orderItem->order->id,
            'url' => url(route('advertiser.order-details', $this->orderItem->order->id)),
        ];
    }
}
