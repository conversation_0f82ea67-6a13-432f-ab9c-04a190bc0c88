<?php

namespace App\Notifications\States;

use App\Models\MarketplaceSingleOrderItem;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class RequirementReminderPublisher extends Notification implements ShouldQueue
{
    use Queueable;

    public $item;

    public function __construct(MarketplaceSingleOrderItem $item)
    {
        $this->item = $item;
    }

    public function via($notifiable)
    {
        return ['mail', 'database'];

    }
    public function toDatabase($notifiable)
    {
        return [
            'order_item_id' => $this->item->id,
            'website_domain' => $this->item->website->website_domain,
            'message' => 'Requirement is still awaiting your approval.',
        ];
    }

    public function toMail($notifiable)
    {
        return (new MailMessage())
            ->subject('Requirement Awaiting Publisher Approval - Reminder')
            ->greeting('Hello ' . ($notifiable->name ?? 'Publisher') . ',')
            ->line('Requirement from ' . $this->item->order->user->name ?? 'N/A' . ' is still awaiting publisher approval.')
            ->line('Please take a moment to review and approve the requirement.')
            ->line('We will notify the buyer once you’ve taken action.')
            ->line('Thank you for your attention!');
    }
}
