<?php

namespace Pressbear\Events;

use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;

class BrowserLoggedOut implements ShouldBroadcast
{
    public $sessionId;

    public function __construct($sessionId)
    {
        $this->sessionId = $sessionId;
    }

    public function broadcastOn()
    {
        return new PrivateChannel('logout-session.' . $this->sessionId);
    }

    public function broadcastAs()
    {
        return 'session.logout';
    }

    public function broadcastWith()
    {
        return [
            'sessionId' => $this->sessionId,
            'timestamp' => now()->toISOString(),
        ];
    }
}
