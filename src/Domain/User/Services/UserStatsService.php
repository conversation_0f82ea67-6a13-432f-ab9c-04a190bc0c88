<?php

namespace Domain\User\Services;

use App\Enums\Role;
use App\Models\User;
use Illuminate\Pagination\LengthAwarePaginator;

class UserStatsService
{
    /*********************************************************************
     * GET FILTERED USERS - Admin index page with search and pagination
     *********************************************************************
     *
     * Retrieves filtered and paginated users for admin dashboard.
     * - Excludes super admin users from results
     * - Applies search filters by name and email
     * - Supports role-based filtering and sorting
     * - Handles soft-deleted user filtering
     *
     * @param array $filters - Filter parameters including search, role, sorting
     * @return LengthAwarePaginator - Paginated user collection
     *
     *********************************************************************/
    public function getFilteredUsers(array $filters): LengthAwarePaginator
    {
        // -----------------------
        // Initialize Query
        $query = User::with('country')
            ->where('role', '!=', Role::SuperAdmin->value);

        // -----------------------
        // Apply Filters and Sorting
        $this->applyFilters($query, $filters);
        $this->applySorting($query, $filters);

        // -----------------------
        // Paginate Results
        $perPage = $filters['perPage'] ?? 10;
        return $query->paginate($perPage);
    }





    /*********************************************************************
     * APPLY FILTERS - Query filtering logic for user search
     *********************************************************************
     *
     * Applies various filters to the user query including:
     * - Trash status filtering (active, trashed, all)
     * - Keyword search across name and email fields
     * - Role-based filtering
     *
     * @param \Illuminate\Database\Eloquent\Builder $query - Eloquent query builder
     * @param array $filters - Filter parameters to apply
     * @return void
     *
     *********************************************************************/
    private function applyFilters($query, array $filters): void
    {
        // -----------------------
        // Trash Status Filter
        if (! empty($filters['trashed'])) {
            match ($filters['trashed']) {
                'trashed' => $query->onlyTrashed(),
                'all' => $query->withTrashed(),
                default => null, // 'active' - do nothing
            };
        }

        // -----------------------
        // Keyword Search Filter
        if (! empty($filters['keyword'])) {
            $query->where(function ($subQuery) use ($filters): void {
                $subQuery->where('name', 'like', "%{$filters['keyword']}%")
                    ->orWhere('email', 'like', "%{$filters['keyword']}%");
            });
        }

        // -----------------------
        // Role Filter
        if (! empty($filters['role'])) {
            $query->where('role', $filters['role']);
        }
    }





    /*********************************************************************
     * APPLY SORTING - Query sorting logic for user results
     *********************************************************************
     *
     * Applies sorting to the user query with validation:
     * - Validates sort field against allowed fields
     * - Sets default sorting to ID descending
     * - Supports custom sort direction
     *
     * @param \Illuminate\Database\Eloquent\Builder $query - Eloquent query builder
     * @param array $filters - Filter parameters containing sort options
     * @return void
     *
     *********************************************************************/
    private function applySorting($query, array $filters): void
    {
        $sortField = $filters['sortField'] ?? 'id';
        $sortDirection = $filters['sortDirection'] ?? 'desc';
        $allowedSorts = ['id', 'name', 'email', 'role'];

        if (in_array($sortField, $allowedSorts, true)) {
            $query->orderBy($sortField, $sortDirection);
        } else {
            $query->orderBy('id', 'desc');
        }
    }
}
