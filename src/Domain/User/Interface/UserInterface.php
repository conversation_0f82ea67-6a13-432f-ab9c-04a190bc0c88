<?php

namespace Domain\User\Interface;

use App\Models\User;
use Domain\User\DataTransferObjects\UserData;
use Illuminate\Pagination\LengthAwarePaginator;

interface UserInterface
{
    /*********************************************************************
     * CREATE USER - New user creation with data processing
     *********************************************************************
     *
     * Creates a new user with proper data processing:
     * - Hashes password securely
     * - Processes address data into JSON format
     * - Returns created user instance
     *
     * @param UserData $payload - User data including password and address info
     * @return User - Created user model instance
     *
     *********************************************************************/
    public function createUser(UserData $payload): User;




    /*********************************************************************
     * UPDATE USER - Existing user modification with data processing
     *********************************************************************
     *
     * Updates existing user with proper data processing:
     * - Hashes password securely if provided
     * - Processes address data into JSON format
     * - Handles email verification status changes
     * - Returns updated user instance
     *
     * @param User $user - User model to update
     * @param UserData $payload - Updated user data including password and address info
     * @return User - Updated user model instance
     *
     *********************************************************************/
    public function updateUser(User $user, UserData $payload): User;




    /*********************************************************************
     * SOFT DELETE USER - Soft delete user with data preservation
     *********************************************************************
     *
     * Performs soft delete operation on user:
     * - Preserves user data in database
     * - Sets deleted_at timestamp
     * - Allows for potential restoration
     *
     * @param User $user - User model to soft delete
     * @return bool - Success status of delete operation
     *
     *********************************************************************/
    public function softDeleteUser(User $user): bool;




    /*********************************************************************
     * RESTORE USER - Restore soft deleted user
     *********************************************************************
     *
     * Restores a soft deleted user:
     * - Removes deleted_at timestamp
     * - Returns success status
     *
     * @param User $user - User model to restore
     * @return bool - Success status of restore operation
     *
     *********************************************************************/
    public function restoreUser(User $user): bool;




    /*********************************************************************
     * FORCE DELETE USER - Permanently delete user
     *********************************************************************
     *
     * Permanently deletes a user:
     * - Removes all user data from database
     * - Returns success status
     *
     * @param User $user - User model to permanently delete
     * @return bool - Success status of delete operation
     *
     *********************************************************************/
    public function forceDeleteUser(User $user): bool;




    /*********************************************************************
     * GET USER ACTIVITY LOGS - Formatted activity history for admin view
     *********************************************************************
     *
     * Retrieves and formats user activity logs:
     * - Includes causer relationship data
     * - Formats dates for display
     * - Processes timestamp properties in log data
     * - Handles pagination for large activity sets
     *
     * @param User $user - User to get activity logs for
     * @param int $perPage - Number of logs per page
     * @return LengthAwarePaginator - Paginated activity log collection
     *
     *********************************************************************/
    public function getUserActivityLogs(User $user, int $perPage = 10): LengthAwarePaginator;
}
