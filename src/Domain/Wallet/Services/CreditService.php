<?php

declare(strict_types=1);

namespace Domain\Wallet\Services;

use App\Models\User;
use Bavix\Wallet\Models\Transaction;
use Domain\Wallet\DataTransferObjects\CreditUserData;
use Domain\Wallet\Resources\CreditResource;
use Domain\Wallet\Resources\UserResource;
use Domain\Wallet\Wallet;
use Illuminate\Support\Facades\DB;

class CreditService
{
    /*************************************************
     * CONSTRUCTOR
    /************************************************/
    public function __construct(
        private Wallet $wallet,
    ) {}

    /**********************************************************************
     * GET USERS FOR CREDIT
     **********************************************************************
     *
     * Retrieves all users that can receive wallet credits.
     *
     * @return \Illuminate\Support\Collection
     * Collection of users with formatted data
     *
     **********************************************************************/
    public function getUsersForCredit()
    {
        $users = User::select('id', 'name', 'email')->with('wallet')
            ->orderBy('id')
            ->get();

        return UserResource::collection($users);
    }

    /**********************************************************************
     * GET USER FOR CREDIT
     **********************************************************************
     *
     * Retrieves a specific user with wallet information for crediting.
     *
     * @param int $id
     * The ID of the user to retrieve
     *
     * @return array
     * User data with wallet information
     *
     **********************************************************************/
    public function getUserForCredit($id)
    {
        $user = User::select('id', 'name', 'email')->with('wallet')->findOrFail($id);

        return UserResource::make($user);
    }

    /**********************************************************************
     * CREDIT USER WALLET
     **********************************************************************
     *
     * Credits a user's wallet with the specified amount.
     *
     * @param int $userId
     * The ID of the user to credit
     *
     * @param array $data
     * Credit data including amount and reason
     *
     * @return bool
     * Success status of the credit operation
     *
     **********************************************************************/
    public function creditUserWallet(array $data)
    {
        // -----------------------
        // Validate Data
        $creditUserData = CreditUserData::from($data);

        try {
            DB::beginTransaction();

            $user = User::findOrFail($creditUserData->user_id);

            // -----------------------
            // Credit Wallet
            $this->wallet->credit($user, $creditUserData->amount, [
                'reference' => 'Admin Credit',
                'type' => 'admin_credit',
                'message' => $creditUserData->reason ?? 'Admin credit',
            ]);

            // -----------------------
            // Commit Transaction
            DB::commit();
            return true;
        } catch (\Exception $e) {
            // -----------------------
            // Rollback Transaction
            DB::rollBack();
            return false;
        }
    }

    /**********************************************************************
     * GET RECENT CREDITS
     **********************************************************************
     *
     * Retrieves recent admin credit transactions.
     *
     * @return array
     * Array of recent credit transactions
     *
     **********************************************************************/
    public function getRecentCredits()
    {
        $transactions = Transaction::with('wallet.holder')
            ->where('type', 'deposit')
            ->where('meta->type', 'admin_credit')
            ->orderBy('created_at', 'desc')
            ->limit(config('pagination.per_page'))
            ->get();

        return CreditResource::collection($transactions)->resolve();
    }
}
