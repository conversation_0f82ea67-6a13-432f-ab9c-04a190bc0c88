<?php

namespace Domain\Wallet\Actions;

use App\Models\User;

/*********************************************************************
 * TRANSFER CREDIT ACTION
 *********************************************************************
 *
 * Transfers a credit from one user's wallet to another.
 *
 * @param User $fromUser
 * The user to transfer the credit from
 *
 * @param User $toUser
 * The user to transfer the credit to
 *
 * @param array $data
 * Transfer data including amount and reason
 *
 * @return void
 *
 *********************************************************************/
class TransferCredit
{
    /*************************************************
     * __invoke
    /************************************************/
    public function __invoke(User $fromUser, User $toUser, array $data): void
    {
        $amount = (int) round($data['amount'] * 100);

        $fromUser->wallet->transfer($toUser->wallet, $amount, [
            'reference' => $data['reference'] ?? 'Wallet Transfer',
            'type' => $data['type'] ?? 'wallet_transfer',
            'message' => $data['message'] ?? 'Wallet transferred',
        ]);
    }
}
