<?php

namespace Domain\Wallet\Actions;

use App\Models\User;

/*********************************************************************
 * CREDIT WALLET ACTION
 *********************************************************************
 *
 * Sends a credit to a user's wallet.
 *
 * @package Domain\Wallet\Actions
 *
 * Credits a user's wallet with the specified amount.
 *
 * @param User $user
 * The user to credit
 *
 * @param array $data
 * Credit data including amount and reason
 *
 * @return void
 *
 *********************************************************************/
class CreditWallet
{
    /*************************************************
     * __invoke
    /************************************************/
    public function __invoke(User $user, array $data): void
    {
        $amount = (int) round($data['amount'] * 100);

        $user->wallet->deposit($amount, [
            'reference' => $data['reference'] ?? 'Wallet Credit',
            'type' => $data['type'] ?? 'wallet_credit',
            'message' => $data['message'] ?? 'Wallet credited',
        ]);
    }
}
