<?php

declare(strict_types=1);

namespace Domain\Wallet\Resources;

use App\Models\User;
use Bavix\Wallet\Models\Wallet;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin \App\Models\User
 * Resource for formatting user data in wallet responses
 *
 * @property User $resource
 * @property-read Wallet|null $wallet
 * @property-read int $balance
 * @property-read bool $active
 */

class UserResource extends JsonResource
{
    /*************************************************
     * toArray
    /************************************************/
    public function toArray($request): array
    {
        return [
            'id' => $this->getKey(),
            'name' => $this->name,
            'email' => $this->email,
            'wallet_balance' => $this->wallet ? number_format($this->wallet->balance / 100, 2) : 0,
        ];
    }
}
