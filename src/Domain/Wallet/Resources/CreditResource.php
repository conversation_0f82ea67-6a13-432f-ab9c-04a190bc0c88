<?php

declare(strict_types=1);

namespace Domain\Wallet\Resources;

use App\Models\User;
use Bavix\Wallet\Models\Transaction;
use Bavix\Wallet\Models\Wallet;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin Transaction
 * Resource for formatting credit transactions
 *
 * @property Transaction $resource
 * @property-read User $holder
 * @property-read int $id
 * @property-read Wallet|null $wallet
 */
class CreditResource extends JsonResource
{
    /*************************************************
     * toArray
    /************************************************/
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'user_name' => $this->wallet?->holder?->name ?? 'Unknown User',
            'amount' => number_format($this->amount / 100, 2),
            'reason' => $this->meta['message'] ?? 'Admin credit',
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
        ];
    }
}
