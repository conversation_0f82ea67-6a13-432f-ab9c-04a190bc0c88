<?php

namespace Domain\Cart\List;

use App\Models\MarketplaceCartItem;
use Illuminate\Database\Eloquent\Collection;

class GetCartItemsByIds
{
    /*********************************************************************
     * GET CART ITEMS BY IDS - RETRIEVE CART ITEMS BY IDS
     *********************************************************************
     *
     * This class retrieves marketplace cart items by their IDs.
     *
     * Purpose:
     * - Fetches specific cart items from the database using an array of IDs
     * - Supports eager loading of relationships to optimize database queries
     * - Returns a collection of MarketplaceCartItem models
     *
     * Usage:
     * - Used in cart management operations where specific items need to be retrieved
     * - Commonly used in checkout processes, cart updates, and order processing
     *
     *********************************************************************/
    public function handle(array $ids, array $with = []): Collection
    {
        return MarketplaceCartItem::with($with)
            ->whereIn('id', $ids)
            ->get();
    }
}
