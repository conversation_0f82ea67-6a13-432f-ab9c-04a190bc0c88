<?php

namespace Domain\Cart\List;

use App\Models\User;
use Domain\Cart\Resources\UserCartItemResource;

/*********************************************************************
 * GET USER CART LIST
 *********************************************************************
 *
 * Retrieves and formats a user's shopping cart data.
 * This class handles the retrieval of cart items for a specific user
 * and formats them with pricing and statistical information.
 *
 * Responsibilities:
 * - Load user's cart items with relationships
 * - Format cart items using resources
 * - Calculate cart totals and statistics
 * - Return structured cart data
 *
 *********************************************************************/
class GetUserCart
{
    /*********************************************************************
     * HANDLE USER CART RETRIEVAL
     *********************************************************************
     *
     * Retrieves and formats a user's complete cart data including
     * items, pricing, and statistical information.
     *
     * @param User $user - The user whose cart to retrieve
     * @return array - Structured cart data with items, stats, and user_id
     *
     *********************************************************************/
    public static function handle(User $user): array
    {
        $user->load('cartItems');
        $cartItems = $user->cartItems->map(fn($item) => UserCartItemResource::make($item)->resolve())->toArray();

        // Calculate totals
        $totalPrice = array_sum(array_column($cartItems, 'price'));


        return [
            'items' => $cartItems,
            'stats' => [
                'totalPrice' => $totalPrice,
                'count' => count($cartItems),
            ],
            'user_id' => $user->id,
        ];
    }
}
