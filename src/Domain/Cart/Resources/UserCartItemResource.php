<?php

declare(strict_types=1);

namespace Domain\Cart\Resources;

use App\Models\MarketplaceCartItem;
use App\Models\MarketplaceWebsite;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Resource for formatting cart items in user cart responses
 *
 * @property MarketplaceCartItem $resource
 * @property-read MarketplaceWebsite $website
 * @property-read int $id
 * @property-read string $niche
 * @property-read bool $content_writing
 */
class UserCartItemResource extends JsonResource
{
    /*************************************************
     * toArray
    /************************************************/
    public function toArray($request): array
    {
        $basePrice = $this->website->nichePrice($this->niche) ?? 0;
        $contentWritingFee = $this->content_writing === 0 ? config('pressbear.content_writing_fee', 50) : 0;

        return [
            'website_id' => $this->website->getKey(),
            'cart_item_id' => $this->id,
            'website' => $this->website->website_domain,
            'niche' => $this->niche,
            'acceptNiches' => $this->website->acceptsNicheList(),
            'content_writing' => $this->content_writing,
            'price' => $basePrice + $contentWritingFee,
            'base_price' => $basePrice,
            'content_writing_fee' => $contentWritingFee,
        ];
    }
}
