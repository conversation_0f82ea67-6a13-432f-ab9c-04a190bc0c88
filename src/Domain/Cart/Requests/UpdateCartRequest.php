<?php

namespace Domain\Cart\Requests;

use App\Rules\ValidNiche;
use Domain\Cart\DataTransferObjects\UpdateCartData;
use Illuminate\Foundation\Http\FormRequest;

class UpdateCartRequest extends FormRequest
{
    /*************************************************
     * Rules
    /************************************************/
    public function rules(): array
    {
        return [
            'cartWebsiteId' => 'required|numeric|exists:marketplace_websites,id',
            'niche' => ['required_if:task,updateNiche', 'string', 'nullable', 'max:40', new ValidNiche()],
            'contentWriting' => 'required_if:task,updateContentWriting|boolean',
            'task' => 'required|string|max:40|in:updateNiche,deleteCartItem,updateContentWriting',
        ];
    }

    /*********************************************************************
     * TO DTO - CONVERT REQUEST TO DATA TRANSFER OBJECT
     *********************************************************************
     *
     * Converts the validated request data to an UpdateCartData DTO.
     *
     *********************************************************************/
    public function toDto(): UpdateCartData
    {
        return UpdateCartData::from($this);
    }
}
