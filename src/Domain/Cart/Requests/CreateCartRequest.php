<?php

namespace Domain\Cart\Requests;

use App\Rules\ValidNiche;
use Domain\Cart\DataTransferObjects\CartData;
use Illuminate\Foundation\Http\FormRequest;

class CreateCartRequest extends FormRequest
{
    /*********************************************************************
     * AUTHORIZE - CHECK IF USER IS AUTHORIZED TO CREATE A CART
     *********************************************************************
     *
     * Checks if the user is authorized to create a cart.
     *
     *********************************************************************/
    public function authorize(): bool
    {
        return true;
    }

    /*********************************************************************
     * RULES - GET THE VALIDATION RULES THAT APPLY TO THE REQUEST
     *********************************************************************
     *
     * Gets the validation rules that apply to the request.
     *
     *********************************************************************/
    public function rules(): array
    {
        return [
            'website' => 'required|integer|exists:marketplace_websites,id',
            'niche' => ['required', 'string', 'max:40', new ValidNiche()],
        ];
    }

    /*********************************************************************
     * TO DTO - CONVERT REQUEST TO DATA TRANSFER OBJECT
     *********************************************************************
     *
     * Converts the validated request data to a CartData DTO.
     *
     *********************************************************************/
    public function toDto(): CartData
    {
        return CartData::from($this);
    }
}
