<?php

namespace Domain\Order\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class OrderDetailRequest extends FormRequest
{
    /*************************************************
     * Determine if the user is authorized to make this request.
    /************************************************/
    public function authorize()
    {
        return Auth::check();
    }

    /*************************************************
     * Rules
    /************************************************/
    public function rules()
    {
        return [
            'id' => 'required|exists:marketplace_orders,id',
        ];
    }
}
