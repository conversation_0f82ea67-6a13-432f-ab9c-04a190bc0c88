<?php

namespace Domain\Order\Notifications;

use Barryvdh\DomPDF\Facade\Pdf;
use Domain\Order\Mail\OrderConfirmation;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Notifications\Messages\BroadcastMessage;
use Illuminate\Notifications\Notification;

class OrderCreated extends Notification implements ShouldQueue
{
    use Queueable;

    public $order;

    /*************************************************
     * Order Data
    /************************************************/
    public function __construct($orderData)
    {
        $this->order = $orderData;
    }


    /*************************************************
     * Notification Types
     *
     * @return array<int, string>
     **************************************************/
    public function via(object $notifiable): array
    {
        return ['mail', 'database', 'broadcast'];
    }


    /*************************************************
     * Mailable Class
     **************************************************/
    public function toMail(object $notifiable): Mailable
    {
        $pdf = Pdf::loadView('pdf.order-confirmation', [
            'order' => $this->order,
            'user' => $this->order->user,
            'items' => $this->order->orderItems,
        ]);

        return (new OrderConfirmation($this->order))
            ->attachData($pdf->output(), 'order-confirmation.pdf')
            ->to($notifiable->email);
    }


    /***************************************************
     * TO DB FOR Ui.
     ****************************************************/
    public function toDatabase(object $notifiable): array
    {
        return [
            'title' => 'Order Placed',
            'message' => 'Please provide the requirements to start the order. #order id: ' . $this->order->id,
            'url' => url(route('advertiser.order-details', $this->order->id)),
            'order_id' => $this->order->id,
        ];
    }


    /*************************************************
     * Broadcast Event
     **************************************************/
    public function toBroadcast(object $notifiable): BroadcastMessage
    {
        return new BroadcastMessage([
            'title' => 'Order Placed',
            'message' => 'Please provide the requirements to start the order. #order id: ' . $this->order->id,
            'url' => url(route('advertiser.order-details', $this->order->id)),
            'order_id' => $this->order->id,
        ]);
    }
}
