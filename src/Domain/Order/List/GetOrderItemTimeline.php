<?php

namespace Domain\Order\List;

use App\Enums\OrderItemStates;
use App\Models\MarketplaceSingleOrderItem;
use Illuminate\Support\Collection;

class GetOrderItemTimeline
{
    /**
     * @var array<int, array{state: string, label: string}>
     */
    protected array $steps = [
        ['state' => OrderItemStates::RequirementsPending->value,                  'label' => 'Order Placed'],
        ['state' => OrderItemStates::RequirementAwaitingPublisherApproval->value, 'label' => 'Requirements'],
        ['state' => OrderItemStates::ContentPending->value,                       'label' => 'Approve Requirements'],
        ['state' => OrderItemStates::ContentAwaitingPublisherApproval->value,     'label' => 'Content'],
        ['state' => OrderItemStates::PublicationInProcess->value,                 'label' => 'Approval'],
        ['state' => OrderItemStates::PublicationDelivered->value,                 'label' => 'Published'],
        ['state' => OrderItemStates::OrderItemCompleted->value,                   'label' => 'Completed'],
        ['state' => OrderItemStates::OrderItemCancelled->value,                   'label' => 'Cancelled'],
        ['state' => OrderItemStates::RefundedToWallet->value,                   'label' => 'Refunded'],
    ];

    /*******************************************************************
     * ORDER ITEM TIMELINE
     *********************************************************************
     *
     * Builds a sequential timeline of order-item steps.
     * Determines the current state index.
     * Marks all steps up to and including current as completed.
     * Returns an array of ['id','label','state','completed'] entries.
     *
     * @param  MarketplaceSingleOrderItem  $orderItem
     *   The order item whose state drives the timeline.
     *
     * @return array<int, array{id: int, label: string, state: string, completed: bool}>
     *   A list of timeline steps with completion flags.
     *
     *******************************************************************/
    public function handle(MarketplaceSingleOrderItem $orderItem): array
    {
        // Show cancelled state only for specific states
        $showCancelledState = in_array($orderItem->state_name, [
            OrderItemStates::RequirementsPending->value,
            OrderItemStates::RequirementAwaitingPublisherApproval->value,
            OrderItemStates::RefundedToWallet->value,
        ]);

        // Show refunded state only for specific states
        $showRefundedState = in_array($orderItem->state_name, [
            OrderItemStates::OrderItemCancelled->value,
            OrderItemStates::RefundedToWallet->value,
        ]);


        $filteredSteps = Collection::make($this->steps)
            ->filter(function (array $step) use ($showCancelledState, $showRefundedState): bool {
                // Show cancelled state only for specific states
                if ($step['state'] === OrderItemStates::OrderItemCancelled->value) {
                    return $showCancelledState;
                } elseif ($step['state'] === OrderItemStates::RefundedToWallet->value) {
                    return $showRefundedState;
                }
                return true;
            });

        // Hide Some States
        if (in_array($orderItem->state_name, [
            OrderItemStates::OrderItemCancelled->value,
            OrderItemStates::RefundedToWallet->value,
        ])) {

            $hideStates = [
                OrderItemStates::ContentPending->value,
                OrderItemStates::ContentAwaitingPublisherApproval->value,
                OrderItemStates::PublicationInProcess->value,
                OrderItemStates::PublicationDelivered->value,
            ];

            $filteredSteps = $filteredSteps->filter(fn(array $step): bool => !in_array($step['state'], $hideStates));
        }

        $state = $this->normalizeState($orderItem->state_name);

        $currentIndex = $filteredSteps
            ->pluck('state')
            ->search($state, true);

        // Build and return the timeline from filtered steps, auto-assigning IDs and completion flags
        return $filteredSteps
            ->values() // ensure continuous integer keys
            ->map(fn(array $step, int $index): array => [
                'id' => $index + 1,
                'label' => $step['label'],
                'state' => $step['state'],
                'completed' => $index <= $currentIndex,
            ])
            ->all();
    }




    /*******************************************************************
     * NORMALIZE STATE
     *********************************************************************
     *
     * Normalizes the state to the most relevant state for the timeline.
     *
     * @param  string  $state
     *   The state to normalize.
     *
     * @return string
     *   The normalized state.
     *
     *******************************************************************/
    private function normalizeState(string $state): string
    {
        return match (true) {
            in_array($state, [
                OrderItemStates::ContentAssignedToWriter->value,
                OrderItemStates::ContentAdvertiserReview->value,
                OrderItemStates::ContentAwaitingPublisherApproval->value,
                OrderItemStates::ContentRevisionRequestedByAdvertiser->value,
            ]) => OrderItemStates::ContentPending->value,

            in_array($state, [
                OrderItemStates::PublicationInProcess->value,
                OrderItemStates::PublicationDelivered->value,
                OrderItemStates::PublicationRevisionRequestedByAdvertiser->value,
            ]) => OrderItemStates::PublicationDelivered->value,

            default => $state,
        };
    }
}
