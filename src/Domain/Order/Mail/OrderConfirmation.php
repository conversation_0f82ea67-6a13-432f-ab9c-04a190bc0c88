<?php

namespace Domain\Order\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class OrderConfirmation extends Mailable
{
    use Queueable;
    use SerializesModels;

    /*************************************************
     * Order Data
    /************************************************/
    public function __construct(public $order)
    {
        //
    }

    /*************************************************
     * Get the message envelope.
    /************************************************/
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Order Confirmation',
        );
    }

    /*************************************************
     * Get the message content definition.
    /************************************************/
    public function content(): Content
    {
        return new Content(
            markdown: 'emails.order-confirmation',
        );
    }

    /*************************************************
     * Get the attachments for the message.
    /************************************************/
    public function attachments(): array
    {
        return [];
    }
}
