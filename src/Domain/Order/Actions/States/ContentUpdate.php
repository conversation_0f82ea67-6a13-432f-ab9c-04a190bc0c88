<?php

namespace Domain\Order\Actions\States;

use App\Enums\OrderItemStates;
use App\Models\MarketplaceSingleOrderItem;
use Domain\Order\Actions\StateTransitions;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ContentUpdate
{
    /*********************************************************************
     * HANDLE
     **********************************************************************
     * Create or update content and transition to next state.
     *
     * @param MarketplaceSingleOrderItem $orderItem
     * @param array                       $data
     * @return MarketplaceSingleOrderItem
     *********************************************************************/
    public function handle(
        MarketplaceSingleOrderItem $orderItem,
        array $data,
    ): MarketplaceSingleOrderItem {
        DB::beginTransaction();

        try {
            // Normalize inputs
            $mediaIds = $data['media_ids'] ?? $data['files_array'] ?? [];
            $nextState = array_key_exists('next_state', $data)
                ? $data['next_state']
                : OrderItemStates::ContentAwaitingPublisherApproval->value;

            // Build payload once
            $payload = [
                'content_source' => $data['content_source'] === 1 ? 'customer' : 'team',
                'title' => $data['title'],
                'content_body' => $data['content_body'],
                'content_url' => $data['content_url'],
                'files_array' => $mediaIds,
                'comments' => $data['comments'],
                'writer_id' => $data['content_source'] === 1
                    ? Auth::id()
                    : ($data['writer_id'] ?? Auth::id()),
            ];

            // Create or update in one shot
            $content = $orderItem->content()
                ->updateOrCreate(
                    ['order_item_id' => $orderItem->id],
                    $payload,
                );

            // Attach media if any
            if (! empty($mediaIds)) {
                $content->attachMedia($mediaIds);
            }

            // Transition state (only if nextState is not null)
            $orderItem->refresh();
            if ($nextState && $orderItem->state_name !== $nextState) {
                $orderItem = (new StateTransitions())
                    ->handle($orderItem, $nextState);
            }

            DB::commit();

            return $orderItem;
        } catch (\Exception $e) {
            DB::rollBack();
            throw new \Exception(
                'Error updating or creating content: ' . $e->getMessage(),
            );
        }
    }



}
