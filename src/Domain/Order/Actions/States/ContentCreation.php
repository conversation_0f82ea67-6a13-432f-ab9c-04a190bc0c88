<?php

namespace Domain\Order\Actions\States;

use App\Models\MarketplaceSingleOrderItem;
use App\Models\User;
use Domain\Order\Actions\StateTransitions;

class ContentCreation
{
    /*********************************************************************
     * HANDLE CONTENT CREATION - PENDING
     *********************************************************************
     * Create initial content when none exists.
     * Assign writer: customer or team.
     * Delegate state transition.
     *
     * @param MarketplaceSingleOrderItem $orderItem
     * @param string                     $newState
     * @return MarketplaceSingleOrderItem
     *********************************************************************/
    public function handle(MarketplaceSingleOrderItem $orderItem, string $newState): MarketplaceSingleOrderItem
    {
        // Load order user
        $orderItem->load('order.user');


        // Create content if missing
        if (! $orderItem->content) {
            $writer = User::where('role', 'writer')->first();

            $orderItem->content()->create([
                'title' => '',
                'content_body' => '',
                'content_url' => '',
                'comments' => '',
                'advertiser_revision_reason' => '',
                'publisher_advertiser_revision_reason' => '',
                'order_item_id' => $orderItem->id,
                'writer_id' => $orderItem['is_content_provided_by_customer']
                    ? $orderItem->order->user->id
                    : $writer->id,
                'files_array' => [],
                'content_source' => 'team',
            ]);
        }


        // State transition
        return (new StateTransitions())->handle($orderItem, $newState);
    }
}
