<?php

namespace Domain\Order\Actions;

use App\Models\MarketplaceSingleOrderItem;
use Domain\Order\Actions\States\RequirementsUpdate;

class UpdateOrderItemRequirements
{
    /*********************************************************************
     * UPDATE ORDER ITEM REQUIREMENTS
     **********************************************************************
     * Admin requirements update via RequirementsUpdate action.
     *
     * @param MarketplaceSingleOrderItem $item
     * @param array                       $requirementsData
     * @return MarketplaceSingleOrderItem
     *********************************************************************/
    public function __invoke(
        MarketplaceSingleOrderItem $item,
        array $requirementsData,
    ): MarketplaceSingleOrderItem {
        // Prepare data
        $data = array_merge($requirementsData, [
            'next_state' => null,
        ]);

        // Update requirements
        return (new RequirementsUpdate())->handle($item, $data);
    }

}
