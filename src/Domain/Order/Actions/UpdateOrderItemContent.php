<?php

namespace Domain\Order\Actions;

use App\Models\MarketplaceSingleOrderItem;
use App\States\OrderItem\ContentAdvertiserReview;
use Domain\Order\Actions\States\ContentUpdate;

class UpdateOrderItemContent
{
    /*********************************************************************
     * UPDATE ORDER ITEM CONTENT
     **********************************************************************
     * Admin content update via ContentUpdate action.
     *
     * @param MarketplaceSingleOrderItem $item
     * @param array                       $contentData
     * @return MarketplaceSingleOrderItem
     *********************************************************************/
    public function __invoke(
        MarketplaceSingleOrderItem $item,
        array $contentData
    ): MarketplaceSingleOrderItem
    {
        // Determine next state based on current state
        $nextState = null;

        // Only transition to ContentAdvertiserReview if not already in that state
        if ($item->state_name !== 'content-advertiser-review') {
            $nextState = ContentAdvertiserReview::class;
        }

        // Prepare data
        $data = array_merge($contentData, [
            'content_source' => 0,
            'files_array'    => $contentData['files_array'] ?? [],
            'next_state'     => $nextState,

        ]);

        // Update content
        return (new ContentUpdate())->handle($item, $data);
    }

}
