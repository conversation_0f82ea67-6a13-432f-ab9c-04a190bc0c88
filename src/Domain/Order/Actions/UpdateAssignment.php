<?php

namespace Domain\Order\Actions;

use App\Models\MarketplaceSingleOrderItem;
use Domain\Order\Actions\States\WriterAssignment;

class UpdateAssignment
{
    /*********************************************************************
     * UPDATE ASSIGNMENT
     **********************************************************************
     * Update writer assignment and state.
     *
     * @param MarketplaceSingleOrderItem $item
     * @param array                       $assignmentData
     * @return MarketplaceSingleOrderItem
     *********************************************************************/
    public function __invoke(
        MarketplaceSingleOrderItem $item,
        array $assignmentData,
    ): MarketplaceSingleOrderItem {
        // Prepare data
        $data = [
            'writer_id' => $assignmentData['writer_id'],
            'next_state' => $assignmentData['state'],
        ];

        // Assign writer and transition state
        return (new WriterAssignment())->handle($item, $data);
    }

}
