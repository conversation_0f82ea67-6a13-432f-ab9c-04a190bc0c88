<?php

namespace Domain\Order\Modules;

use App\Models\MarketplaceOrder;
use App\Models\MarketplaceSingleOrderItem;
use Domain\Order\List\{GetLatestOrders, GetOrderById, GetOrderItemById, GetOrderItemTimeline, GetOrderWithSearch};
use Illuminate\Pagination\LengthAwarePaginator;

trait OrdersTraits
{
    /*************************************************
     * Get Latest Orders
    /************************************************/
    public function getLatestOrdersListWithPagination(array $with = []): LengthAwarePaginator
    {
        return (new GetLatestOrders())->handle()->with($with)->paginate(config('pressbear.advertiser_default_pagination'));
    }

    /*************************************************
     * Get Latest Orders
    /************************************************/
    public function getOrderById(int $id, array $with = []): MarketplaceOrder
    {
        return (new GetOrderById())->handle($id, $with);
    }


    /*************************************************
     * Get Order Item By Id
    /************************************************/
    public function getOrderItemById(int $id, array $with = []): MarketplaceSingleOrderItem
    {
        return (new GetOrderItemById())->handle($id, $with);
    }

    /*************************************************
     * Get Order Item Timeline
    /************************************************/
    public function getOrderItemTimeline(MarketplaceSingleOrderItem $orderItem): array
    {
        return (new GetOrderItemTimeline())->handle($orderItem);
    }

    /*************************************************
     * Get Order List With Search
    /************************************************/
    public function getOrderListWithSearch($search)
    {
        return (new GetOrderWithSearch())->handle($search);
    }
}
