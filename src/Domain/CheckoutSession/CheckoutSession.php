<?php

namespace Domain\CheckoutSession;

use App\Models\MarketplaceCheckoutSession;
use App\Models\MarketplaceOrder;
use Domain\CheckoutSession\Actions\CheckoutSuccess;
use Domain\CheckoutSession\Actions\CreateCheckoutSession;
use Domain\CheckoutSession\List\GetByPaymentIntentId;

class CheckoutSession
{
    /*********************************************************************
     * GET CHECKOUT SESSION - GET CHECKOUT SESSION BY PAYMENT INTENT ID
     *********************************************************************
     *
     * Gets a checkout session by payment intent id and user id.
     *
     * @param string $paymentIntentId
     * @return MarketplaceCheckoutSession
     *
     *********************************************************************/
    public function GetByPaymentIntentId(string $paymentIntentId, int $userId): MarketplaceCheckoutSession
    {
        return (new GetByPaymentIntentId())->handle($paymentIntentId, $userId);
    }

    /*************************************************
     * CHECKOUT SUCCESS
     *
     * Updates the checkout session and sends an order notification.
     *
     * @param MarketplaceCheckoutSession $sessionData
     * @param MarketplaceOrder $order
     * @return void
     *
    /************************************************/
    public function checkoutSuccess(MarketplaceCheckoutSession $sessionData, MarketplaceOrder $order)
    {
        return (new CheckoutSuccess())->handle($sessionData, $order);
    }

    /*************************************************
     * CREATE CHECKOUT SESSION
     *
     * Creates a checkout session for the user.
     *
     * @param array $data
     * @return MarketplaceCheckoutSession
     *
    /************************************************/
    public function createCheckoutSession(): array
    {
        return (new CreateCheckoutSession())->handle();
    }
}
