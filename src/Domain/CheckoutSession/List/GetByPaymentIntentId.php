<?php

namespace Domain\CheckoutSession\List;

use App\Models\MarketplaceCheckoutSession;

class GetByPaymentIntentId
{
    /*********************************************************************
     * GET CHECKOUT SESSION - GET CHECKOUT SESSION BY PAYMENT INTENT ID AND USER ID
     *********************************************************************
     *
     * Gets a checkout session by payment intent id and user id.
     *
     * @param string $paymentIntentId
     * @param int $userId
     * @return MarketplaceCheckoutSession
     *
     *********************************************************************/
    public function handle(string $paymentIntentId, int $userId): MarketplaceCheckoutSession
    {
        $marketplaceCheckoutSession = MarketplaceCheckoutSession::with('user')
            ->where('stripe_payment_intent', $paymentIntentId)
            ->where('checkout_successful', false)
            ->where('user_id', $userId)
            ->first();

        if (! $marketplaceCheckoutSession) {
            throw new \Exception('Checkout session not found for payment intent: ' . $paymentIntentId);
        }

        return $marketplaceCheckoutSession;
    }
}
