<?php

namespace Domain\CheckoutSession\Actions;

use App\Models\MarketplaceCheckoutSession;
use Domain\Cart\Cart as CartService;
use Domain\Payment\Payment;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Stripe\PaymentIntent;

/*********************************************************************
 * CREATE CHECKOUT SESSION ACTION
 *********************************************************************
 *
 * Handles the creation of checkout sessions for the marketplace.
 * This action orchestrates the checkout process by collecting cart data,
 * creating payment intents, and storing session information for order
 * creation after successful payment.
 *
 * Responsibilities:
 * - Validates cart data and ensures items are present
 * - Creates payment intents with payment providers
 * - Stores checkout session data for order processing
 * - Handles error scenarios and redirects appropriately
 *
 *********************************************************************/
class CreateCheckoutSession
{
    private CartService $cartService;
    private Payment $payment;

    public function __construct()
    {
        $this->cartService = new CartService();
        $this->payment = new Payment();
    }

    /*********************************************************************
     * CREATE CHECKOUT SESSION
     **********************************************************************
     *
     * We Create a checkout session to handle payment and order creation
     * once user proceed to payment page:
     *
     * 1. We collect all the current item ids in cart.
     * 2. Calculate amount to charge.
     * 3. Create a payment intent with stripe for processing payment.
     * 4. Store checkout session in db to use it as reference point
     *  ..for order creation after order success.
     *
     *
     *********************************************************************/
    public function handle()
    {
        try {
            // -----------------------
            // Get Cart Data
            $cartData = $this->cartService->getUserCart();


            // -----------------------
            // Check: if Cart is Empty send to home route.
            if (
                empty($cartData['items']) ||
                ($cartData['stats']['count'] < 1 ||
                    $cartData['stats']['totalPrice'] < 1)
            ) {
                return redirect()->route('home');
            }

            // -----------------------
            // Create Stripe Payment Intent
            $paymentIntent = $this->payment->createPaymentIntent(
                $cartData['stats']['totalPrice'],
                Auth::user(),
                config('pressbear.env.default_payment_method'),
            );

            // Check if payment intent creation was successful
            if (! $paymentIntent || ! is_object($paymentIntent)) {
                throw new \Exception('Failed to create payment intent');
            }

            /** @var PaymentIntent $paymentIntent */
            $cartData['stripeClientSecret'] = $paymentIntent->client_secret;


            // -----------------------
            // Pick Cart Items
            $cartItemsIds = Arr::pluck($cartData['items'], 'cart_item_id');


            // --------------------------------
            // Create & Store Checkout Session
            $cartData['Session'] = MarketplaceCheckoutSession::create([
                'user_id' => $cartData['user_id'],
                'cart_items_ids' => $cartItemsIds,
                'total_amount' => $cartData['stats']['totalPrice'],
                'total_items' => $cartData['stats']['count'],
                'order_memo' => Session::get('order_memo') ?? null,
                'checkout_ip_address' => request()->ip() ?? null,
                'stripe_payment_intent' => $paymentIntent->id,
            ]);

            return $cartData;
        } catch (\Exception $e) {
            throw new \Exception('Session Creation Error: ' . $e->getMessage());
        }
    }
}
