<?php

namespace Domain\CheckoutSession\Address;

use App\Models\User;
use Domain\CheckoutSession\Address\Actions\UpdateAddress;

class Address
{
    /*********************************************************************
     * ADDRESS
     **********************************************************************
     *
     * Represents the user's address information.
     *
     *********************************************************************/
    public function updateAddress(array $data, User $user): bool
    {
        return (new UpdateAddress())->handle($data, $user);
    }
}
