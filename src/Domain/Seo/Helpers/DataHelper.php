<?php

namespace Domain\Seo\Helpers;

use App\Models\Keyword;
use App\Models\MarketplaceWebsite;
use App\Models\MarketplaceWebsiteCategory;

class DataHelper
{
    /*********************************************************************
     * FIND CATEGORY FROM DB TO ATTACH TO SITE
     *
     * get category data from similarweb
     * find the category id from db
     *
     * @param $category string (default: other)
     *********************************************************************/
    public static function findCategoryID($categoryText = 'other')
    {

        $categoryText = str_replace('_', ' ', $categoryText); //replace _
        $haveSubCategory = strpos($categoryText, '/');

        if ($haveSubCategory) {
            $categorise = explode('/', $categoryText);
            $category['parent'] = $categorise[0];
            $category['child'] = $categorise[1];
        } else {
            $category['parent'] = $categoryText;
        }


        // find category by child or parent
        if ($haveSubCategory) {
            $findCategory = MarketplaceWebsiteCategory::where('category', $category['child'])->first();
        } else {
            $findCategory = MarketplaceWebsiteCategory::where('category', $category['parent'])->first();
        }

        // if no category found - (other category id)
        if (! $findCategory) {
            $findCategory = MarketplaceWebsiteCategory::where('category', 'other')->first();
        }

        return $findCategory->getKey();
    }



    /**********************************************
     * Create & Attach Keywords
     *
     * Create and attach topics, if exist then
     * update and attach.
     *
     * @param $topics Array
     * @param $website MarketplaceWebsite
     *
     * @return results
     **/
    public static function attachKeywords(MarketplaceWebsite $website, $keywords)
    {

        // Insert or update keywords
        // updating because we need to update keyword volumes n cpc in future
        foreach ($keywords as $keyword) {
            Keyword::upsert(
                [
                    'name' => $keyword['keyword'],
                    'cpc' => self::normaliseNumber($keyword['cost-per-click']),
                    'volume' => $keyword['search-volume'],
                ],
                ['name'], //unique
                ['cpc', 'volume'],
            ); //to update
        }

        // again call and get ids
        $keywordNames = collect($keywords)->pluck('Name');
        $KeywordIDs = Keyword::whereIn('name', $keywordNames)->select('id')->get();


        // Attach topics
        return $website->keyword_website()->sync($KeywordIDs);
    }



    /*********************************************************************
     * NORMALISE NUMBER
     *
     * Normalise the number to a percentage
     *
     * @param $number float
     * @return float
     *********************************************************************/
    public static function normaliseNumber($number)
    {
        return round($number * 100, 2);
    }
}
