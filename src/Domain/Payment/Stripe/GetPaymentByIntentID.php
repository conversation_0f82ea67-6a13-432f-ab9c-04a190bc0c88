<?php

namespace Domain\Payment\Stripe;

use Domain\Payment\Exceptions\PaymentFailedException;
use Illuminate\Support\Facades\Log;
use Stripe\StripeClient;

class GetPaymentByIntentID
{
    /*************************************************
     * Constructor
    /************************************************/
    private $STRIPE_CLIENT;

    public function __construct()
    {
        $this->STRIPE_CLIENT = config('services.stripe.client');
    }

    /*************************************************
     * Handle
    /************************************************/
    public function handle(string $paymentIntentId): object
    {
        try {
            // -----------------------
            // initiate stripe
            $stripe = new StripeClient($this->STRIPE_CLIENT);

            // Returning after redirecting to a payment method portal.
            return $stripe->paymentIntents->retrieve($paymentIntentId);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            throw new PaymentFailedException();
        }
    }
}
