<?php

// Right now this  code is not per our standards, we need to refactor it to be more modular and testable.

namespace Domain\Writer;

use App\Models\MarketplaceSingleOrderItem;
use Domain\Writer\Traits\{WriterFiltersTrait, WriterStatsTrait};
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Cache;

/*********************************************************************
 * ADMIN WRITER ASSIGNMENTS SERVICE
 *********************************************************************
 *Manages writer assignment operations for admin panel including
 * ..filtering, searching, and statistics for marketplace order items.
 *
 * Provides methods to:
 * - Get filtered and paginated writer assignments
 * - Calculate assignment statistics
 * - Retrieve available content states
 *********************************************************************/
class AdminWriterService
{
    use WriterFiltersTrait;
    use WriterStatsTrait;


    /*******************************************************************
     * GET ASSIGNMENTS WITH FILTERS
     *********************************************************************
     *
     * Retrieves and filters marketplace order items for writer assignments
     * ..with search, sorting, and pagination capabilities.
     *
     * Supports filtering by:
     * - Assignment status (assigned/unassigned, completed, upcoming)
     * - Order state
     * - Search terms (writer name/email, website domain)
     * - Custom sorting and pagination
     *
     * @param array $filters - Filter parameters including perPage, assignmentStatus,
     * orderStatus, sortField, sortOrder, searchTerm
     *
     * @return LengthAwarePaginator
     * Paginated collection of marketplace order items with content status appended
     *
     *******************************************************************/
    public function getAssignments(array $filters): LengthAwarePaginator
    {
        // -----------------------
        // Extract Filter Parameters
        $perPage = $filters['perPage'] ?? 10;
        $assignmentStatus = $filters['assignmentStatus'] ?? 'assigned_unassigned';
        $orderState = $filters['orderStatus'] ?? null;
        $sortField = $filters['sortField'] ?? 'id';
        $sortOrder = $filters['sortOrder'] ?? 'desc';
        $searchTerm = $filters['searchTerm'] ?? '';


        // -----------------------
        // Initialize Base Query
        $query = MarketplaceSingleOrderItem::query()
            ->with(['website', 'content.writer'])
            ->where('is_content_provided_by_customer', false)
            ->when($assignmentStatus, fn($q) => $this->applyAssignmentStatusFilter($q, $assignmentStatus))
            ->when($searchTerm, fn($q) => $this->applySearchFilter($q, $searchTerm))
            ->when($orderState, fn($q) => $q->where('state', $orderState));

        return $query->orderBy($sortField, $sortOrder)
            ->paginate($perPage);
    }




    /*******************************************************************
     * GET ASSIGNMENT STATISTICS
     *********************************************************************
     *
     * Calculates and returns statistics for writer assignments across
     * ..different states and categories.
     *
     * Returns counts for:
     * - In progress assignments
     * - Pending assignments
     * - Items in revision
     * - Upcoming assignments
     *
     * @return array
     * Array containing assignment statistics with descriptive keys
     *
     *******************************************************************/
    public function getAssignmentStats(): array
    {
        return Cache::remember('assignment_stats', config('pressbear.oneMinuteCache'), fn() => [
            'in_progress' => $this->getInProgressCount(),
            'pending_assignment' => $this->getPendingAssignmentCount(),
            'in_revision' => $this->getInRevisionCount(),
            'upcoming' => $this->getUpcomingCount(),
        ]);
    }





    /*******************************************************************
     * GET CONTENT STATES
     *********************************************************************
     *
     * Returns formatted content states for use in UI components
     * ..with value and label pairs.
     *
     * Transforms enum values into human-readable labels by:
     * - Converting kebab-case to Title Case
     * - Creating value-label pairs for dropdowns/selects
     *
     * @return array
     * Array of content states with 'value' and 'label' keys
     *
     *******************************************************************/
    public function getContentStates(): array
    {
        return collect($this->contentStates)->map(fn($stateValue) => [
            'value' => $stateValue,
            'label' => ucwords(str_replace('-', ' ', $stateValue)),
        ])->toArray();
    }
}
