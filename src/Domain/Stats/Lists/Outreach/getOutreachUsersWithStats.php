<?php

namespace Domain\Stats\Lists\Outreach;

use App\Enums\Role;
use App\Models\User;
use App\Traits\Filters\DateFilterTrait;
use Illuminate\Pagination\LengthAwarePaginator;

class getOutreachUsersWithStats
{
    use DateFilterTrait;


    /**********************************************************************
     * HANDLE REQUEST
     **********************************************************************
     *
     * Retrieve filtered, paginated outreach users with stats.
     * - Applies date filters by status dates.
     *
     * @param  array                   $filters  Filter params
     * @return LengthAwarePaginator             Paginated results
     **********************************************************************/
    public function handle(array $filters): LengthAwarePaginator
    {
        // -----------------------
        // Initialize Base Query
        $query = User::where('role', Role::Outreach->value);


        // -----------------------
        // Add Outreach Counts
        $query->withCount([
            'outreaches as inprogress_count' => function ($q) use ($filters): void {
                $q->where('status', 'inprogress');
                $this->applyDateFilter($q, $filters, 'created_at');
            },
            'outreaches as onboarded_count' => function ($q) use ($filters): void {
                $q->where('status', 'onboarded');
                $this->applyDateFilter($q, $filters, 'onboarded_at');
            },
            'outreaches as rejected_count' => function ($q) use ($filters): void {
                $q->where('status', 'rejected');
                $this->applyDateFilter($q, $filters, 'rejected_at');
            },
        ]);


        // -----------------------
        // Apply Sorting
        $query->orderByDesc('id');


        // -----------------------
        // Paginate Results
        return $query->paginate(config('pressbear.default_pagination_10'))->withQueryString();
    }
}
