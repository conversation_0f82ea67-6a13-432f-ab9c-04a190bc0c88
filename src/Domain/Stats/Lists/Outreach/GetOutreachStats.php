<?php

namespace Domain\Stats\Lists\Outreach;

use Domain\Stats\Actions\Outreach\CalculateOutreachStats;

/*********************************************************************
 * OUTREACH STATISTICS SERVICE
 **********************************************************************
 *
 * Provides methods to retrieve outreach statistics for individual
 * users and aggregated totals across all users
 *
 *********************************************************************/
class GetOutreachStats
{
    /*********************************************************************
     * GET OUTREACH STATS
     **********************************************************************
     *
     * Retrieve outreach statistics for a specific user with optional
     * date filtering
     *
     * @param int|null $userId - The user ID to get stats for
     * @param array $filters - Date filters (start_date, end_date)
     * @return array<string,int> - Status counts and total
     *
     *******************************************************************
     */
    public function __invoke(?int $userId, array $filters): array
    {
        return (new CalculateOutreachStats())->handle($userId, $filters);
    }
}
