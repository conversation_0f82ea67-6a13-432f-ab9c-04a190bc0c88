<?php

namespace Domain\Stats\Lists;

use Domain\Stats\Actions\Sales\CalculateSalesStats;
use Domain\Stats\Actions\Sales\CalculateUserSalesStats;

// Sales and order statistics - uses Actions for calculations
class GetSalesStats
{
    /*********************************************************************
     * STATS FOR TIME PERIOD
     **********************************************************************
     *
     * Get comprehensive sales stats for time period
     *
     * @param array $filters
     * @return array
     *
     *********************************************************************/
    public function getStatsForTimePeriod(array $filters): array
    {
        return (new CalculateSalesStats())->handle($filters);
    }




    /*********************************************************************
     * STATS BY USER
     **********************************************************************
     *
     * Get sales stats for specific user
     *
     * @param int $userId
     * @param array $filters
     * @return array
     *
     *********************************************************************/
    public function getStatsByUser(int $userId, array $filters): array
    {
        return (new CalculateUserSalesStats())->handle($userId, $filters);
    }
}
