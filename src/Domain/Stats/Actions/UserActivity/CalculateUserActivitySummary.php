<?php

namespace Domain\Stats\Actions\UserActivity;

use App\Enums\Role;
use App\Models\MarketplaceAdminWebsite;
use App\Models\MarketplaceOrder;
use App\Models\Outreach;
use App\Models\User;
use App\Traits\Filters\DateFilterTrait;

/**
 * CALCULATE USER ACTIVITY SUMMARY
 *
 * Role-based user activity metrics and statistics.
 */
class CalculateUserActivitySummary
{
    use DateFilterTrait;




    /*********************************************************************
     * HANDLE USER ACTIVITY CALCULATION
     **********************************************************************
     *
     * Fetches user and calculates role-specific activity metrics.
     *
     * @param int $userId - target user ID
     * @param array $filters - date range filters
     * @return array - user info and activity summary
     *
     *********************************************************************/
    public function handle(int $userId, array $filters): array
    {
        // -----------------------
        // Fetch User Data
        $user = User::select(['id','name','email','role'])->find($userId);
        if (! $user) {
            return [
                'error' => 'User not found',
                'user_info' => null,
                'activity_summary' => null,
            ];
        }


        // -----------------------
        // Extract User Info
        $info = $user->only(['id','name','email','role']);


        // -----------------------
        // Calculate Role-Specific Activity
        $activity = match ($info['role']) {
            Role::Outreach->value => $this->outreachActivity($userId, $filters),
            Role::Advertiser->value => $this->advertiserActivity($userId, $filters),
            Role::Publisher->value => $this->publisherActivity($userId, $filters),
            default => $this->generalActivity($userId, $filters),
        };


        // -----------------------
        // Return Combined Results
        return [
            'user_info' => $info,
            'activity_summary' => $activity,
        ];
    }




    /*********************************************************************
     * OUTREACH ACTIVITY
     **********************************************************************
     *
     * Calculates outreach metrics including success rate.
     *
     * @param int $userId - user ID
     * @param array $filters - date range filters
     * @return array - outreach activity metrics
     *
     *********************************************************************/
    private function outreachActivity(int $userId, array $filters): array
    {
        // -----------------------
        // Get Outreach Statuses
        $statuses = $this
            ->applyDateFilter(Outreach::where('user_id', $userId), $filters)
            ->pluck('status');


        // -----------------------
        // Calculate Counts And Metrics
        $counts = $statuses->countBy();
        $total = $statuses->count();
        $onb = $counts->get('onboarded', 0);


        // -----------------------
        // Return Activity Summary
        return [
            'role_type' => 'outreach',
            'total_outreach' => $total,
            'in_progress' => $counts->get('inprogress', 0),
            'onboarded' => $onb,
            'success_rate' => $total
                ? round($onb / $total * 100, 2) //percentage calculation
                : 0,
        ];
    }




    /*********************************************************************
     * ADVERTISER ACTIVITY
     **********************************************************************
     *
     * Calculates advertiser spending metrics and order statistics.
     *
     * @param int $userId - user ID
     * @param array $filters - date range filters
     * @return array - advertiser activity metrics
     *
     *********************************************************************/
    private function advertiserActivity(int $userId, array $filters): array
    {
        // -----------------------
        // Build Query With Filters
        $query = MarketplaceOrder::where('user_id', $userId);
        $query = $this->applyDateFilter($query, $filters);


        // -----------------------
        // Calculate Order Metrics
        $totalOrders = $query->count();
        $totalSpent = $query->sum('price_paid');
        $avgValue = $totalOrders
            ? number_format($totalSpent / $totalOrders, 2)
            : '0.00';


        // -----------------------
        // Return Activity Summary
        return [
            'role_type' => 'advertiser',
            'total_orders' => $totalOrders,
            'total_spent' => number_format($totalSpent, 2),
            'average_order_value' => $avgValue,
        ];
    }




    /*********************************************************************
     * PUBLISHER ACTIVITY
     **********************************************************************
     *
     * Calculates publisher website count and earnings.
     *
     * @param int $userId - user ID
     * @param array $filters - date range filters
     * @return array - publisher activity metrics
     *
     *********************************************************************/
    private function publisherActivity(int $userId, array $filters): array
    {
        // -----------------------
        // Build Query With Filters
        $query = MarketplaceAdminWebsite::where('publisher_user_id', $userId);
        $query = $this->applyDateFilter($query, $filters);


        // -----------------------
        // Calculate Publisher Metrics
        $totalWebsites = $query->count();
        $totalEarnings = $query->sum('price_paid');


        // -----------------------
        // Return Activity Summary
        return [
            'role_type' => 'publisher',
            'total_websites' => $totalWebsites,
            'total_earnings' => number_format($totalEarnings, 2),
        ];
    }




    /*********************************************************************
     * GENERAL ACTIVITY
     **********************************************************************
     *
     * Basic activity count for users without specific role metrics.
     *
     * @param int $userId - user ID
     * @param array $filters - date range filters
     * @return array - general activity metrics
     *
     *********************************************************************/
    private function generalActivity(int $userId, array $filters): array
    {
        // -----------------------
        // Calculate Basic Activity Count
        $total = $this
            ->applyDateFilter(User::where('id', $userId), $filters)
            ->count();


        // -----------------------
        // Return Activity Summary
        return [
            'role_type' => 'general',
            'total_activity' => $total,
        ];
    }
}
