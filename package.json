{"private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "dev:all": "node open-dev-terminals.js"}, "devDependencies": {"@alpinejs/focus": "^3.10.5", "@tailwindcss/forms": "^0.5.2", "@tailwindcss/typography": "^0.5.0", "@vue/tsconfig": "^0.7.0", "alpinejs": "^3.12.3", "autoprefixer": "^10.4.7", "axios": "^1.7.2", "laravel-echo": "^2.0.2", "laravel-vite-plugin": "^1.2.0", "postcss": "^8.4.14", "pusher-js": "^8.4.0", "tailwindcss": "^3.1.0", "typescript": "^5.8.2", "vite": "^6.2.0"}, "dependencies": {"@alpinejs/collapse": "^3.14.1", "@alpinejs/mask": "^3.14.0", "@alpinejs/morph": "^3.14.0", "@fontsource/inter": "^5.0.20", "@headlessui/vue": "^1.7.23", "@heroicons/vue": "^2.2.0", "@inertiajs/vue3": "^2.0.3", "@tiptap/extension-placeholder": "^3.0.9", "@tiptap/starter-kit": "^3.0.9", "@tiptap/vue-3": "^3.0.9", "@vitejs/plugin-vue": "^5.2.1", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dropzone": "^6.0.0-beta.2", "human-number": "^2.0.4", "lodash": "^4.17.21", "lucide-vue-next": "^0.477.0", "reka-ui": "^2.2.1", "sass": "^1.85.1", "select2": "^4.1.0-rc.0", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "vue": "^3.5.13", "vue-chartjs": "^5.3.2", "vue3-toastify": "^0.2.8", "ziggy-js": "^2.5.2"}}