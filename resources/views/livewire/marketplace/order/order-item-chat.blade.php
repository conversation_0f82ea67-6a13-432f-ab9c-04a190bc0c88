<div>
    <div class="flex flex-col h-full">
        <!-- Chat Display -->
        <div class="p-4 border rounded-t-xl bg-white flex flex-col gap-3 max-h-[400px] overflow-y-auto" x-data="{
            scrollToBottom() {
                $nextTick(() => {
                    const container = $el;
                    container.scrollTop = container.scrollHeight;
                });
            }
        }" x-init="scrollToBottom()" @scroll-to-bottom.window="scrollToBottom()" wire:init="loadMessages">
            <h3 class="text-lg font-semibold text-gray-800">Chat</h3>
            @if (count($messages) === 0)
            <div class="flex-1 flex items-center justify-center min-h-[200px]">
                <div class="text-center text-gray-500">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto mb-2" fill="none"
                        viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                    <p class="text-sm">No messages yet</p>
                    <p class="text-xs mt-1">Start the conversation</p>
                </div>
            </div>
            @else
            @foreach ($messages as $message)
            <div class="flex gap-2 {{ $message['sender_id'] === auth()->id() ? 'justify-end' : 'justify-start' }}">
                @if ($message['sender_id'] !== auth()->id())
                <div class="flex-shrink-0">
                    <img src="{{ isset($message['sender']['profile_photo_path']) && $message['sender']['profile_photo_path'] ? Storage::url($message['sender']['profile_photo_path']) : asset('assets/avatars/man.svg') }}"
                        alt="{{ $message['sender']['name'] ?? 'User' }}" class="w-8 h-8 rounded-full object-cover">
                </div>
                @endif

                <div class="flex flex-col {{ $message['sender_id'] === auth()->id() ? 'items-end' : 'items-start' }}">
                    <div
                        class="{{ $message['sender_id'] === auth()->id() ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-900' }} p-3 rounded-xl w-fit max-w-[75%]">
                        <div class="whitespace-pre-line">{{ $message['text'] }}</div>
                        @if (isset($message['attachment_path']) && $message['attachment_path'])
                        <div class="mt-1">
                            <a href="{{ Storage::url($message['attachment_path']) }}" target="_blank"
                                class="text-sm {{ $message['sender_id'] === auth()->id() ? 'text-blue-200' : 'text-blue-600' }} underline">
                                View Attachment
                            </a>
                        </div>
                        @endif
                    </div>

                    <div class="flex items-center gap-1 mt-1">
                        @if ($message['sender_id'] !== auth()->id())
                        <span class="text-xs text-gray-500">{{ $message['sender']['name'] ?? 'User' }}</span>
                        @endif
                        <span class="text-xs opacity-70">{{
                            \Carbon\Carbon::parse($message['created_at'])->diffForHumans() }}</span>
                    </div>
                </div>

                @if ($message['sender_id'] === auth()->id())
                <div class="flex-shrink-0">
                    <img src="{{ isset($message['sender']['profile_photo_path']) && $message['sender']['profile_photo_path'] ? Storage::url($message['sender']['profile_photo_path']) : asset('assets/avatars/man.svg') }}"
                        alt="{{ $message['sender']['name'] ?? 'User' }}" class="w-8 h-8 rounded-full object-cover">
                </div>
                @endif
            </div>
            @endforeach
            @endif
        </div>

        <!-- Input Form -->
        <form wire:submit="sendMessage" @submit-message.window.prevent="$wire.sendMessage()"
            enctype="multipart/form-data" class="border-t bg-gray-50 rounded-b-xl">
            <div class="flex items-center gap-2 px-4 py-3">
                <label class="relative cursor-pointer group">
                    <input type="file" wire:model="attachment" accept=".jpg,.jpeg,.png,.doc,.docx,.pdf,.txt"
                        class="hidden" />
                    <div class="p-2 text-gray-600 hover:text-blue-600 transition-colors">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                        </svg>
                    </div>
                    @if ($attachment)
                    <div class="flex items-center gap-2 bg-gray-100 px-3 py-1 rounded-lg">
                        <span class="text-sm text-gray-600 truncate max-w-[150px]">{{
                            $attachment->getClientOriginalName() }}</span>
                        <button type="button" wire:click="$set('attachment', null)"
                            class="text-gray-500 hover:text-red-500">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                    @endif
                </label>
                <textarea wire:model.defer="message" x-init="$nextTick(() => $el.focus())" rows="1"
                    placeholder="Type a message..."
                    class="flex-1 resize-none p-2.5 text-sm text-gray-900 bg-white rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500"
                    @keydown.enter.prevent="if (!event.shiftKey) { $dispatch('submit-message') }"
                    @keydown.enter.shift="event.stopPropagation()"></textarea>
                <button wire:loading.attr="disabled" wire:loading.class="opacity-50" wire:target="sendMessage"
                    type="submit"
                    class="p-2 bg-blue-500 text-white rounded-full hover:bg-blue-600 transition relative flex items-center justify-center w-10 h-10">
                    <div wire:loading.remove wire:target="sendMessage">
                        <svg class="w-5 h-5 rotate-90" xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                            viewBox="0 0 18 20">
                            <path
                                d="m17.914 18.594-8-18a1 1 0 0 0-1.828 0l-8 18a1 1 0 0 0 1.157 1.376L8 18.281V9a1 1 0 0 1 2 0v9.281l6.758 1.689a1 1 0 0 0 1.156-1.376Z" />
                        </svg>
                    </div>
                    <div wire:loading.flex wire:target="sendMessage"
                        class="absolute inset-0 flex items-center justify-center">
                        <svg class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none"
                            viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4">
                            </circle>
                            <path class="opacity-75" fill="currentColor"
                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                            </path>
                        </svg>
                    </div>
                </button>
            </div>
        </form>
    </div>
</div>