<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <h2 class="text-2xl font-bold text-gray-900">Withdraw</h2>
    </div>

    <!-- Balance Display -->
    <div class="max-w-2xl mx-auto mb-8">
        <div class="bg-white p-6 rounded-lg shadow">
            <div class="flex flex-col items-start gap-2">
                <span class="text-sm font-medium text-gray-600">Available Balance</span>
                <span class="text-3xl font-bold text-gray-900" wire:key="balance">${{ $this->balance }} <span
                        class="text-sm font-normal text-gray-500">USD</span></span>
            </div>
        </div>
    </div>

    <div class="max-w-2xl mx-auto">
        <div class="bg-white p-6 rounded-lg shadow">
            <form wire:submit="withdraw">
                <!-- Amount Input -->
                <div class="mb-6">
                    <label for="amount" class="block text-sm font-medium text-gray-700 mb-2">
                        Withdrawal Amount
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <span class="text-gray-500 sm:text-sm">$</span>
                        </div>
                        <input type="number" id="amount" wire:model="amount"
                            class="block w-full pl-7 rounded-md border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm @error('amount') border-red-500 @enderror"
                            placeholder="0.00" step="0.01" />
                    </div>
                    @error('amount') <p class="mt-2 text-sm text-red-600">{{ $message }}</p> @enderror
                </div>

                <!-- Submit Button -->
                <div>
                    <button type="submit"
                        class="w-full flex justify-center py-2.5 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
                        wire:loading.attr="disabled">
                        <span wire:loading>Processing...</span>
                        <span wire:loading.remove>Withdraw Funds</span>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Withdrawal Requests History -->
    <div class="mt-8">
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Withdrawal History</h3>
            </div>
            <div class="overflow-x-auto">
                <table class="w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                ID</th>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Amount</th>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Status</th>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Date</th>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Details</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @forelse($this->withdrawalRequests as $request)
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">#{{ $request->id }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${{
                                number_format($request->amount / 100, 2) }}</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2.5 py-1 inline-flex text-xs leading-5 font-semibold rounded-full 
                                    {{ $request->status === 'pending' ? 'bg-yellow-100 text-yellow-800' : 
                                       ($request->status === 'approved' ? 'bg-green-100 text-green-800' : 
                                       'bg-red-100 text-red-800') }}">
                                    {{ ucfirst($request->status) }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ $request->created_at_formatted }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                <button wire:click="showRequestDetails({{ $request->id }})"
                                    class="text-indigo-600 hover:text-indigo-900">
                                    View Details
                                </button>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="5" class="px-6 py-8 whitespace-nowrap text-sm text-gray-500 text-center">
                                No withdrawal requests found
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Request Details Modal -->
    @if($selectedRequest)
    <div class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 max-w-lg w-full mx-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Withdrawal Request Details</h3>
                <button wire:click="closeRequestDetails" class="text-gray-400 hover:text-gray-500">
                    <span class="sr-only">Close</span>
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="space-y-4">
                <div>
                    <dt class="text-sm font-medium text-gray-500">Request ID</dt>
                    <dd class="mt-1 text-sm text-gray-900">#{{ $selectedRequest->id }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Amount</dt>
                    <dd class="mt-1 text-sm text-gray-900">${{ number_format($selectedRequest->amount / 100, 2) }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Status</dt>
                    <dd class="mt-1">
                        <span class="px-2.5 py-1 inline-flex text-xs leading-5 font-semibold rounded-full 
                                {{ $selectedRequest->status === 'pending' ? 'bg-yellow-100 text-yellow-800' : 
                                   ($selectedRequest->status === 'approved' ? 'bg-green-100 text-green-800' : 
                                   'bg-red-100 text-red-800') }}">
                            {{ ucfirst($selectedRequest->status) }}
                        </span>
                    </dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Payment Method</dt>
                    <dd class="mt-1 text-sm text-gray-900">
                        {{ $selectedRequest->paymentMethod ? strtoupper($selectedRequest->paymentMethod->key) : 'Stripe'
                        }}
                    </dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Requested At</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ $selectedRequest->created_at_formatted }}
                    </dd>
                </div>
                @if($selectedRequest->admin_notes)
                <div>
                    <dt class="text-sm font-medium text-gray-500">Admin Notes</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ $selectedRequest->admin_notes }}</dd>
                </div>
                @endif
                @if($selectedRequest->payment_url)
                <div>
                    <dt class="text-sm font-medium text-gray-500">Payment Proof URL</dt>
                    <dd class="mt-1 text-sm text-gray-900">
                        <a href="{{ $selectedRequest->payment_url }}" target="_blank"
                            class="text-indigo-600 hover:text-indigo-900 flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                            </svg>
                            View Payment Proof
                        </a>
                    </dd>
                </div>
                @endif
                @if($selectedRequest->media && $selectedRequest->media->count() > 0)
                <div>
                    <dt class="text-sm font-medium text-gray-500">Payment Proof Files</dt>
                    <dd class="mt-4 space-y-4">
                        @foreach($selectedRequest->media as $file)
                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div class="flex items-center space-x-4">
                                <div class="flex-shrink-0">
                                    <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">{{ $file->name }}</p>
                                    <div class="flex items-center space-x-2 text-xs text-gray-500">
                                        <span>{{ $file->size }}</span>
                                        <span>•</span>
                                        <span>{{ pathinfo($file->name, PATHINFO_EXTENSION) }}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="flex-shrink-0">
                                <a href="{{ $file->url }}" target="_blank" rel="noopener noreferrer"
                                    class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium text-emerald-700 bg-emerald-50 hover:bg-emerald-100 rounded-md transition-colors duration-200">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                    </svg>
                                    View
                                </a>
                            </div>
                        </div>
                        @endforeach
                    </dd>
                </div>
                @endif
            </div>
        </div>
    </div>
    @endif
</div>