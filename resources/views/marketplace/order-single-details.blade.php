<x-app-layout>
  <x-app.header />


  <script>
    //**********************************************
  // Alpine Global Stores
  // Errors: for showing form errors
  // order items: used for updating ui order state

  // Load store when alpinejs init
  document.addEventListener('alpine:init', loadStore);

  // Load store - use during livewire:navigate
  if(Alpine){ loadStore(); }

  function loadStore(){
      Alpine.store('errors', {error: false, id: -1});

      Alpine.store('orderItems', {

        @foreach($order->orderItems as $item)
          {{ $item->id }}: {
            website: '{{ $item->website->website_domain }}',
            state: '{{ $item->state }}'
          },
          @endforeach

        });
  }




  //**********************************************
  // Update Single order details
  // get data and make an update
  // if errors, show error on frontend
  // on success, update alpine store + toast
  function updateOrderData(data){


    // Reset alpine error store first
    Alpine.store('errors').error = false;
    Alpine.store('errors').error.id = -1;


    let updateOrderURL = '{{route('order-update')}}';

    // axios request
    // data structuring
    axios.post(updateOrderURL, {
          content_topic: data.content_topic,
          order_item_id: data.order_item_id,
          is_content_provided_by_customer: data.is_content_provided_by_customer,
          content_url: data.content_url,    
          target_url:  data.target_url,     
          anchor_text: data.anchor_text,     
          customer_comments: data.customer_comments,
      })
      .then(function(response){
        if(response.status == 200){

            // success toast
            toast('Updated', {type: 'success', position: 'bottom-center'});

            // update order status
            websiteID = data.order_item_id;
            Alpine.store('orderItems')[websiteID].state = 'inprogress';
          }           
      })
      .catch(function (error) {
        
        // failure toast
        toast('Update Error', {type: 'warning', position: 'bottom-center'})

        // structuring returned json object into arrays
        var obj     = error.response.data.errors;
        var result  = Object.keys(obj).map((key) => [obj[key]]);

        // set alpine store errors to display
        Alpine.store('errors').error    = result;
        Alpine.store('errors').error.id = data.order_item_id;
      })

  }

  </script>





  <div id="app-body" class="app-body flex">


    <div id="main-section" class="flex flex-col w-full overflow-hidden">
      <div id="body-wrapper" class="my-4 md:m-10">


        {{-- ************************* --}}
        {{-- Top Order Information Box --}}
        <div id="order-details" class="box-section-wrapper">


          {{-- Intro --}}
          <div id="intro-section" class="mb-10">
            <div id="page-title" class="text-2xl font-bold text-slate-800 tracking-tight">
              Order Information
            </div>
            <div id="page-info" class="text-sm text-slate-600 pt-1.5">
              Please find all the information regarding your order here.
            </div>
          </div>


          {{-- Data Info --}}
          <ul class="order-meta space-y-3 columns-1 md:columns-2 lg:columns-3 text-sm">
            <li>
              <span class="o-label font-bold text-slate-600">Order id:</span>
              <span class="o-data text-teal-700 font-medium">#{{$order->id}}</span>
            </li>

            <li>
              <span class="o-label font-bold text-slate-600">Date Ordered:</span>
              <span class="o-data text-teal-700 font-medium">{{date_format($order->created_at,"d M Y") }}</span>
            </li>

            <li>
              <span class="o-label font-bold text-slate-600">Est Delievery:</span>
              <span class="o-data text-teal-700 font-medium">
                {{$order->created_at->addDays(7)->format('d M Y') }}
              </span>
            </li>

            <li>
              <span class="o-label font-bold text-slate-600">Amount Paid:</span>
              <span class="o-data text-teal-700 font-medium">{{Number::currency($order->price_paid)}}</span>
            </li>

            <li>
              <span class="o-label font-bold text-slate-600">Payment Id:</span>
              <span class="o-data text-teal-700 font-medium">
                <a href="#billing-summary">#{{$order->payment_id}}</a>
              </span>
            </li>

            <li>
              <span class="o-label font-bold text-slate-600">Support Email:</span>
              <span class="o-data text-teal-700 font-medium"><EMAIL></span>
            </li>

            <li>
              <span class="o-label font-bold text-slate-600">Order Items Delieverd:</span>
              <span class="o-data text-teal-700 font-medium">
                {{ $orderStatus['delivered']['count'] }}
                of
                {{ $orderStatus['itemsCount'] }}
              </span>
            </li>

            <li class="flex items-center">
              <span class="o-label font-bold text-slate-600 pr-1">Invoice: </span>
              <a target="_blank" href="{{ route('advertiser.show-invoice') }}?id={{$order->id}}"
                class="hidden text-sm font-medium text-teal-700 hover:text-indigo-500 sm:block">
                View invoice
                <span aria-hidden="true"> &rarr;</span>
              </a>
            </li>

            <li class="flex">
              <span class="o-label font-bold text-slate-600 mr-1">Order Status:</span>

              @php
              $statusClass = match($order->status) {
              'delivered' => 'bg-green-100 text-green-800',
              'inprogress' => 'bg-yellow-100 text-yellow-800',
              'pending' => 'bg-yellow-100 text-gray-800',
              'cancelled' => 'bg-red-100 text-red-800',
              default => 'bg-gray-100 text-gray-800',
              };
              @endphp

              <span class="px-2 py-1 rounded-full text-xs font-semibold {{ $statusClass }}">
                {{ ucfirst($order->status) }}
              </span>

            </li>
          </ul>

          {{-- <div id="order-status-top" class="border-t border-zinc-100 mt-2 py-6">
            <h4 class="sr-only">Status</h4>
            <p class="text-xs font-medium text-zinc-500">
              Estimated to be delievered by <time datetime="2021-03-24">March 24, 2023</time>
            </p>
            <div class="mt-4" aria-hidden="true">
              <div class="overflow-hidden rounded-full bg-gray-200">
                <div class="h-2 rounded-full bg-emerald-900 opacity-90" style="width: calc((1 * 3 + 1) / 7 * 100%)">
                </div>
              </div>
              <div class="mt-4 flex flex-row justify-between text-xs font-medium text-zinc-500">
                <div class="text-emerald-900 font-medium">✔ Order placed</div>
                <div class="text-emerald-900 font-medium">✔ Information</div>
                <div class=" text-emerald-900 font-medium">✔ Writing</div>
                <div class="">Publishing</div>
                <div class="">Delivered</div>
              </div>
            </div>
          </div> --}}

          {{-- Action Buttons --}}
          {{-- <div id="actions-buttons"
            class="flex border-dotted border-t-2 border-zinc-200 mt-4 pt-4 space-x-3 justify-between ">
            <a href="#order-items-table-user">
              <button type="button"
                class="py-2.5 px-4 text-sm font-medium text-white focus:outline-none bg-emerald-700 rounded-md border-2 border-emerald-700 hover:bg-emerald-800 hover:border-emerald-800 focus:z-10 focus:ring-2 focus:ring-red-500 dark:focus:ring-red-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">Add
                Order Details</button>
            </a>

            <a href="#order-items-table-user">
              <button type="button"
                class="py-2.5 px-4 text-sm font-medium text-slate-500 focus:outline-none bg-white rounded-md border-2 border-slate-400 hover:bg-emerald-600 hover:text-white hover:border-emerald-600 focus:z-10 focus:ring-2 focus:ring-red-500 dark:focus:ring-orange-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">Start
                My Order</button>
            </a>

            <a href="#order-items-table-user">
              <button type="button"
                class=" py-2.5 px-4 text-sm font-medium text-slate-700 focus:outline-none bg-white rounded-md border-2 border-zinc-200 hover:bg-red-700 hover:text-white focus:z-10 focus:ring-2 focus:ring-red-500 dark:focus:ring-red-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">Request
                Revision</button>
            </a>

          </div> --}}

        </div>



        @if($order->orderItems->contains(fn($item) => get_class($item->state) ===
        \App\States\OrderItem\RequirementsPending::class))
        <x-marketplace.order.pending-requirements-message />
        @endif




        <!-- ORDERS TABLE -->
        <div id="order-items-table-user" style="overflow-x:auto;" class="bg-white border rounded-lg shadow my-12">
          <table class="min-w-full divide-y whitespace-nowrap divide-gray-200 dark:divide-gray-700 border-b bg-white ">

            <thead
              class="bg-gray-200/90 text-gray-900 border-b dark:bg-zinc-900 dark:text-zinc-100 tracking-wide uppercase text-xs font-semibold overflow-scroll">
              <tr>

                <th scope="col" class="px-4 py-3 w-1/4">
                  <div class="flex items-center gap-x-2">
                    <span title="Website where your content will be published.">
                      Website
                    </span>
                  </div>
                </th>

                <th scope="col" class="px-4 py-3 w-1/4">
                  <div class="flex items-center gap-x-2">
                    Order Status
                    <div class="tooltip opacity-30 hover:opacity-100 pt-1">
                      <x-ui.tooltip text="?" tooltip="URL where you post is published." />
                    </div>
                  </div>
                </th>

                <th scope="col" class="px-4 py-3">
                  <div class="flex items-center gap-x-2">
                    Published URL
                    <div class="tooltip opacity-30 hover:opacity-100 pt-1">
                      <x-ui.tooltip text="?" tooltip="URL where you post is published." />
                    </div>
                  </div>
                </th>

                <th scope="col" class="px-4 py-3">
                  <div title="last time this item was updated" class="flex items-center gap-x-2">
                    Last Updated
                  </div>
                </th>

                <th scope="col" class="px-4 py-3">
                  Details
                </th>

              </tr>

            </thead>


            <tbody class="divide-y divide-gray-200 dark:divide-gray-700 text-gray-600 font-medium">

              @foreach($order->orderItems as $item)
              <tr>

                <td class="whitespace-nowrap px-4 py-2">
                  <div x-data @click="$dispatch('toggle', '{{$item->website->website_domain}}')"
                    class="p-4 cursor-pointer">

                    <span class="text-sm font-bold text-emerald-900 hover:underline 
                                flex items-center mr-2">
                      <img width="16px" height="16px"
                        src="https://www.google.com/s2/favicons?sz=64&domain_url={{$item->website->website_domain}}"
                        class="w-4 h-4 border-1 rounded-full mr-1.5">
                      <span class="pr-1 tracking-wide">
                        {{$item->website->website_domain}}
                      </span>
                      <div class="mt-0.5">
                        <x-icons.etc.down-icon />
                      </div>
                    </span>
                  </div>
                </td>

                <td class="whitespace-nowrap px-4 py-2">
                  <span x-data x-text="$store.orderItems[{{$item->id}}].state" class="text-sm text-gray-700"></span>
                </td>

                <td class="whitespace-nowrap px-4 py-2">
                  @if($item->publication->publication_url)
                  <a href="https://{{$item->publication->publication_url}}" class="p-4 flex items-center">
                    <span class="text-sm mr-1">{{$item->publication->publication_url}}</span>
                    <span class="w-4 text-green-600">
                      <x-icons.lucide.external-link />
                    </span>
                  </a>
                  @else
                  <span class="text-xs text-gray-300">Awaiting Publication.</span>
                  @endif
                </td>


                <td class="whitespace-nowrap px-4 py-2">
                  <div class="p-4">
                    <span class="text-sm text-gray-600">
                      {{date_format($item->updated_at,"d M Y") }}
                    </span>
                  </div>
                </td>

                <td class="whitespace-nowrap px-4 py-2">
                  <div class="p-4">
                    <div x-data @click="$dispatch('toggle', '{{$item->website->website_domain}}')" class="button-action block mx-auto py-2 text-xs px-4 bg-emerald-600
                               hover:bg-emerald-700 text-white w-fit rounded-lg cursor-pointer">
                      Details
                    </div>
                  </div>
                </td>

              </tr>
              @endforeach
            </tbody>
          </table>
        </div>
        <!-- End Table -->






        {{-- SINGLE ORDER BOXES --}}

        <div class="space-y-8 mb-12 p-6 bg-white shadow border rounded-lg" x-data="{
              activeAccordion: 'accordion-1', 

              {{-- According set --}}
              setActiveAccordion(id) { 
                  this.activeAccordion = (this.activeAccordion == id) ? '' : id 
              },

              {{-- Table link click toggle and scroll to box --}}
              scrollToggle(websiteID){
                this.setActiveAccordion(websiteID);

                {{-- timeout because of x-collapse duration in scroll --}}
                setTimeout(function() {
                     const element = document.getElementById(websiteID);
                      element.scrollIntoView();
                }, 250);

              } 
            }" @toggle.window="scrollToggle($event.detail)">

          {{-- Intro --}}
          <div id="intro-section" class="mb-10">
            <div id="page-title" class="text-xl font-bold text-gray-700 tracking-tight">
              Requirements
            </div>
            <div id="page-info" class="text-sm text-gray-600 pt-1.5">
              Provide requirments for your publication here.
            </div>
          </div>

          @foreach($order->orderItems as $item)
          <livewire:marketplace.order.order-item-requirements-box :item="$item" />
          @endforeach

        </div>






        {{--
        <x-marketplace.order.order-chat />

        <x-marketplace.order.internal-notes />

        <x-marketplace.order.billing-panel />

        --}}



      </div>



    </div>

  </div>

</x-app-layout>