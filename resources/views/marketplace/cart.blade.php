<x-app-layout>
    <x-app.header :cartItems="$cartItems" />


    {{-- JS TO Update Niche + Delete --}}
    <script type="text/javascript">
        // Cart Updates
        // handle delete, change niche
        // @action: deleteCartITEM / changeNiche
        // first post request update cart
        // then get request get fragment via function
        function updateCartData(action, websiteID, niche = null, contentWriting = false) {

            // progress bar show
            document.querySelector('#cart-progress-bar').style.display = "block";


            // update
            let url = '{{ route('update-cart') }}';
            let cartUrl = '{{ route('cart') }}';

            axios.post(url, {
                    task: action,
                    cartWebsiteId: websiteID,
                    niche: niche,
                    contentWriting: contentWriting
                })
                .then(function(response) {

                    if (response.status == 200) {

                        updateCart = fragmentUpdate(cartUrl + '?fragment=cart', '#content-body');

                        if (updateCart) {
                            toast('Cart Updated', {
                                type: 'success',
                                position: 'bottom-center'
                            })
                        }
                    } else {
                        toast('Error Updating', {
                            type: 'warning',
                            position: 'bottom-center'
                        })
                    }

                });
        }
    </script>



    <div id="app-body" class="flex justify-center flex-col max-w-7xl mx-auto">


        {{-- steps --}}
        @if ($cartStats['count'] > 0)
        <div class="steps py-12 hidden md:flex">
            <x-marketplace.cart.steps steps="1" />
        </div>
        @endif



        {{-- FRAGMENT CART CONTENT --}}
        @fragment('cart-content')

        <div id="content-body">

            {{-- Progress Bar --}}
            <div class="progress-wrap w-full mt-0 md:mt-8 hidden md:block ">
                <div id="cart-progress-bar" class="progressbar w-full"></div>
            </div>


            {{-- Two Sections --}}
            <div class="lg:grid lg:grid-cols-12 md:gap-x-16 lg:items-start lg:gap-x-12 xl:gap-x-16 bg-gray-50 
                md:mb-8 h-auto rounded-lg mx-auto w-full px-6 py-8 md:px-12 md:py-12 ">


                {{-- if cart is not empty --}}
                @if ($cartStats['count'] > 0)
                <section aria-labelledby="cart-heading" class="lg:col-span-7">

                    <div class="pb-6">
                        <h2 id="cart-heading" class="text-2xl font-bold text-gray-900">
                            Items in your cart
                        </h2>
                        <p class="mt-3 text-base text-gray-600 leading-relaxed">
                            You can provide order requirements after payment.
                        </p>
                    </div>


                    {{-- CART ITEMS --}}
                    <ul role="list" class="mt-2">

                        {{-- Loop over cart items --}}
                        @foreach ($cartItems as $item)
                        <li class="flex flex-col gap-4 space-y-2 md:space-y-0 items-center 
                       bg-white py-6 px-4 md:px-8 border justify-between border-gray-200 rounded-lg mb-4">

                            <div class="flex flex-col md:flex-row w-full">
                                {{-- info --}}
                                <div class="flex flex-row w-full  items-center">

                                    {{-- image --}}
                                    <div class="mr-6">
                                        <img src="https://www.google.com/s2/favicons?sz=64&domain_url={{ $item['website'] }}"
                                            class="h-6 w-6 rounded-md object-cover object-center">
                                    </div>

                                    {{-- website --}}
                                    <div class="flex flex-col justify-between">
                                        <div class="flex justify-between">
                                            <div class="text-sm font-bold text-gray-800">
                                                {{ $item['website'] }}
                                            </div>
                                        </div>
                                        <div class="mt-1.5 flex text-xs text-gray-500">
                                            <p class="pr-2">Post</p>
                                            <p class="pl-2 border-l border-gray-200 capitalize">
                                                {{ $item['niche'] }}
                                            </p>
                                        </div>
                                    </div>

                                    {{-- mobile layout price --}}
                                    {{-- <div class="md:hidden text-right p-1">
                                        <p class="mt-1 text-sm font-bold text-gray-700">${{ $item['price'] }}</p>
                                    </div> --}}
                                </div>
                                {{-- niche --}}
                                <div class="flex flex-row w-full gap-2 items-center justify-between mt-3 lg:mt-0">
                                    <div class="">
                                        <select x-data="{ niche: '{{ $item['niche'] }}' }" x-model="niche"
                                            @change="updateCartData('updateNiche', {{ $item['website_id'] }}, niche)"
                                            name="niche"
                                            class="cursor-pointer max-w-full rounded-md border border-gray-300 py-1.5 text-left text-base font-medium leading-5 text-gray-700 shadow-sm focus:border-emerald-500 focus:outline-none focus:ring-1 focus:ring-emerald-500 sm:text-sm hover:bg-gray-50">
                                            @foreach(\App\Enums\Niche::cases() as $niche)

                                            @if(in_array($niche->value, $item['acceptNiches']))
                                            <option value="{{ $niche->value }}" @selected($item['niche']==$niche->
                                                value)>{{ $niche->name }}
                                            </option>
                                            @endif
                                            @endforeach
                                        </select>
                                    </div>

                                    {{-- price --}}
                                    <div class="col-span-1 text-right">
                                        <p class="mt-1 text-sm font-bold text-gray-700">${{ $item['price'] }}</p>
                                        @if($item['content_writing'] == 0 && $item['content_writing_fee'] > 0)
                                        <p class="text-xs text-gray-500">
                                            Base: ${{ $item['base_price'] }} + Writing: ${{ $item['content_writing_fee'] }}
                                        </p>
                                        @endif
                                    </div>

                                    {{-- ACTION: REMOVE FROM CART --}}
                                    <div class="col-span-1 justify-end flex">
                                        <div class="cursor-pointer p-1.5 rounded-full hover:bg-red-100" x-data="{}"
                                            title="delete this item"
                                            @click="updateCartData('deleteCartItem', {{ $item['website_id'] }})">
                                            <x-icons.lucide.trash class="w-4 h-4 stroke-gray-500" />
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {{-- Content Writing Options --}}
                            <div class="w-full mt-4 pt-4 border-t border-gray-200" x-data="{
                                            isOpen: {{ $loop->first ? 'true' : 'false' }},
                                            contentSource: '{{ $item['content_writing'] ?? '0' }}'
                                        }">
                                <div class="w-full bg-gray-50 p-4 rounded-lg">
                                    <div class="flex items-center justify-between cursor-pointer"
                                        :class="{ 'mb-3': isOpen }" @click="isOpen = !isOpen">
                                        <div class="flex items-center space-x-2">
                                            <div class="text-gray-500">
                                                <svg x-show="isOpen" class="w-4 h-4" fill="none" stroke="currentColor"
                                                    viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2" d="M19 9l-7 7-7-7" />
                                                </svg>
                                                <svg x-show="!isOpen" class="w-4 h-4" fill="none" stroke="currentColor"
                                                    viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2" d="M9 5l7 7-7 7" />
                                                </svg>
                                            </div>
                                            <h3 class="text-sm font-medium text-gray-900">Content</h3>
                                        </div>
                                        <span class="text-xs text-gray-500">Required</span>
                                    </div>
                                    <div x-show="isOpen" class="grid grid-cols-1 sm:grid-cols-2 gap-4"
                                        x-transition:enter="transition ease-out duration-200"
                                        x-transition:enter-start="opacity-0 transform -translate-y-2"
                                        x-transition:enter-end="opacity-100 transform translate-y-0"
                                        x-transition:leave="transition ease-in duration-150"
                                        x-transition:leave-start="opacity-100 transform translate-y-0"
                                        x-transition:leave-end="opacity-0 transform -translate-y-2">
                                        <label
                                            class="relative flex items-start p-4 cursor-pointer rounded-lg border border-gray-200 bg-white shadow-sm focus:outline-none hover:border-emerald-500"
                                            :class="{ 'border-emerald-500 ring-1 ring-emerald-500': contentSource === '0' }">
                                            <div class="flex items-center h-5">
                                                <input type="radio" id="write_for_me_{{ $item['website_id'] }}"
                                                    name="content_source_{{ $item['website_id'] }}" value="0"
                                                    class="h-4 w-4 text-emerald-600 border-gray-300 focus:ring-emerald-500"
                                                    x-model="contentSource"
                                                    @change="updateCartData('updateContentWriting', {{ $item['website_id'] }}, null, $event.target.value)">
                                            </div>
                                            <div class="ml-3 flex flex-col">
                                                <span class="block text-sm font-medium text-gray-900">Write content for
                                                    me</span>
                                                <span class="mt-1 flex items-center text-xs text-gray-500">We'll create
                                                    content for your post</span>
                                                @if(config('pressbear.content_writing_fee', 50) > 0)
                                                    <span class="mt-1 flex items-center text-xs text-emerald-600 font-medium">
                                                        +${{ config('pressbear.content_writing_fee', 50) }} writing fee
                                                    </span>
                                                @endif
                                            </div>
                                        </label>

                                        <label
                                            class="relative flex items-start p-4 cursor-pointer rounded-lg border border-gray-200 bg-white shadow-sm focus:outline-none hover:border-emerald-500"
                                            :class="{ 'border-emerald-500 ring-1 ring-emerald-500': contentSource === '1' }">
                                            <div class="flex items-center h-5">
                                                <input type="radio" id="provide_own_content_{{ $item['website_id'] }}"
                                                    name="content_source_{{ $item['website_id'] }}" value="1"
                                                    class="h-4 w-4 text-emerald-600 border-gray-300 focus:ring-emerald-500"
                                                    x-model="contentSource"
                                                    @change="updateCartData('updateContentWriting', {{ $item['website_id'] }}, null, $event.target.value)">
                                            </div>
                                            <div class="ml-3 flex flex-col">
                                                <span class="block text-sm font-medium text-gray-900">I will provide
                                                    content</span>
                                                <span class="mt-1 flex items-center text-xs text-gray-500">You'll
                                                    provide the content for your
                                                    post</span>
                                            </div>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </li>
                        @endforeach

                    </ul>
                </section>




                {{-- ************* --}}
                <!-- Order summary -->
                <section aria-labelledby="summary-heading"
                    class="mt-6 lg:mt-20 rounded-lg border border-gray-200 bg-white shadow-sm px-4 py-6 sm:p-6 lg:col-span-5  lg:p-8">
                    <h2 id="summary-heading" class="text-lg font-medium text-gray-900">Order summary</h2>

                    <dl class="mt-6 space-y-4">
                        <div class="flex items-center justify-between">
                            <dt class="text-sm text-gray-600">Total Posts</dt>
                            <dd class="text-sm font-medium text-gray-900">{{ $cartStats['count'] }} </dd>
                        </div>

                        <div class="flex items-center justify-between border-t border-gray-200 pt-4">
                            <dt class="flex text-sm text-gray-600">
                                <span>Tax estimate</span>
                            </dt>
                            <dd class="text-sm font-medium text-gray-900">$0</dd>
                        </div>
                        <div class="flex items-center justify-between border-t border-gray-200 pt-4">
                            <dt class="text-base font-medium text-gray-900">Order total</dt>
                            <dd class="text-base font-medium text-gray-900">${{ $cartStats['totalPrice'] }}</dd>
                        </div>
                    </dl>

                    <div class="mt-6">
                        <a wire:navigate href="{{ route('address-collect') }}">
                            <button
                                class="w-full rounded-md border border-transparent bg-emerald-500 px-4 py-3 text-base font-medium text-white shadow-sm hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:ring-offset-2 focus:ring-offset-gray-50">Confirm
                                Order</button>
                        </a>
                    </div>
                </section>


                {{-- STATE: Cart Empty --}}
                @else
                <div id="empty-cart" class="col-span-12 flex flex-col justify-center items-center">

                    <img class="w-1/2" src="{{ Storage::url('graphics/illustrations/empty-cart.png') }}">

                    <div class="mb-8 font-medium text-orange-800">
                        No Items in Cart
                    </div>

                    <a href="{{ route('marketplace') }}" class="mb-8" wire:navigate>
                        <button
                            class="border-2 bg-emerald-700 text-white text-sm font-medium px-4 py-2 rounded-lg hover:bg-emerald-800">
                            Go Back To Marketplace
                        </button>
                    </a>

                </div>
                @endif

            </div>

        </div>
        @endfragment


    </div>
</x-app-layout>