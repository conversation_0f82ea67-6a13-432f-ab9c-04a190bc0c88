<x-guest-layout>
    <x-laravel.authentication-card>
        <x-slot name="logo">
            <x-laravel.authentication-card-logo />
        </x-slot>

        <!-- Page Header -->
        <div class="text-center mb-8">
            <h1 class="text-2xl font-semibold text-gray-900 mb-2">Create Account</h1>
            <p class="text-gray-600">Join as an advertiser and grow your business</p>
        </div>

        <x-laravel.validation-errors class="mb-6" />

        <form method="POST" action="{{ route('register') }}" class="space-y-5">
            @csrf
            <x-honeypot />

            <!-- Name Field -->
            <div>
                <x-laravel.label for="name" value="{{ __('Full Name') }}" class="block text-sm font-medium text-gray-900 mb-2" />
                <x-laravel.input id="name"
                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 text-gray-900 placeholder-gray-500"
                    type="text"
                    name="name"
                    :value="old('name')"
                    required
                    autofocus
                    autocomplete="name"
                    placeholder="Enter your full name" />
            </div>

            <!-- Email Field -->
            <div>
                <x-laravel.label for="email" value="{{ __('Email Address') }}" class="block text-sm font-medium text-gray-900 mb-2" />
                <x-laravel.input id="email"
                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 text-gray-900 placeholder-gray-500"
                    type="email"
                    name="email"
                    :value="old('email')"
                    required
                    autocomplete="username"
                    placeholder="<EMAIL>" />
            </div>

            <!-- Password Field -->
            <div>
                <x-laravel.label for="password" value="{{ __('Password') }}" class="block text-sm font-medium text-gray-900 mb-2" />
                <x-laravel.input id="password"
                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 text-gray-900 placeholder-gray-500"
                    type="password"
                    name="password"
                    required
                    autocomplete="new-password"
                    placeholder="Create a secure password" />
            </div>

            <!-- Confirm Password Field -->
            <div>
                <x-laravel.label for="password_confirmation" value="{{ __('Confirm Password') }}" class="block text-sm font-medium text-gray-900 mb-2" />
                <x-laravel.input id="password_confirmation"
                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 text-gray-900 placeholder-gray-500"
                    type="password"
                    name="password_confirmation"
                    required
                    autocomplete="new-password"
                    placeholder="Confirm your password" />
            </div>

            <!-- Terms -->
            @if (Laravel\Jetstream\Jetstream::hasTermsAndPrivacyPolicyFeature())
            <div class="flex items-start">
                <x-laravel.checkbox name="terms" id="terms" required class="mt-1 rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500" />
                <div class="ml-3">
                    <p class="text-sm text-gray-600">
                        {!! __('I agree to the :terms_of_service and :privacy_policy', [
                        'terms_of_service' => '<a target="_blank" href="'.route('terms.show').'"
                            class="font-medium text-indigo-600 hover:text-indigo-500 transition-colors duration-200">'.__('Terms of Service').'</a>',
                        'privacy_policy' => '<a target="_blank" href="'.route('policy.show').'"
                            class="font-medium text-indigo-600 hover:text-indigo-500 transition-colors duration-200">'.__('Privacy Policy').'</a>',
                        ]) !!}
                    </p>
                </div>
            </div>
            @endif

            <!-- Submit Button -->
            <div class="pt-2">
                <x-laravel.button class="w-full justify-center py-3 text-base font-semibold bg-indigo-600 hover:bg-indigo-700 focus:ring-indigo-500">
                    {{ __('Create Account') }}
                </x-laravel.button>
            </div>
        </form>

        <!-- Account Type Options -->
        <div class="mt-6 pt-4 border-t border-gray-200">
            <div class="text-center mb-3">
                <p class="text-sm text-gray-600">Or choose a different account type</p>
            </div>

            <!-- Publisher Registration -->
            <div class="relative group">
                <a href="{{ route('publisher.register') }}"
                   class="block p-3 border-2 border-gray-200 bg-gray-50 rounded-lg hover:border-indigo-300 hover:bg-indigo-50 transition-all duration-200 group-hover:shadow-md">
                    <div class="flex items-center justify-center mb-1">
                        <x-icons.lucide.layout-grid class="w-4 h-4 text-gray-600 group-hover:text-indigo-600 mr-2 transition-colors duration-200" />
                        <span class="text-sm font-semibold text-gray-700 group-hover:text-indigo-700 transition-colors duration-200">Register as Publisher</span>
                    </div>
                    <p class="text-xs text-gray-600 text-center">Monetize your website with our advertising network</p>
                </a>
            </div>
        </div>

        <!-- Sign In Link -->
        <div class="mt-4 text-center">
            <p class="text-sm text-gray-600">
                Already have an account?
                <a class="font-medium text-indigo-600 hover:text-indigo-500 transition-colors duration-200 ml-1"
                   href="{{ route('login') }}">
                    {{ __('Sign in') }}
                </a>
            </p>
        </div>
    </x-laravel.authentication-card>
</x-guest-layout>