<div
    class="min-h-screen flex flex-col justify-center items-center pt-6 sm:py-12 bg-gradient-to-br from-indigo-50 via-white to-cyan-50 relative overflow-hidden">
    <!-- Background Pattern -->
    <div
        class="absolute inset-0 bg-grid-slate-100 [mask-image:linear-gradient(0deg,white,rgba(255,255,255,0.6))] -z-10">
    </div>

    <!-- Floating Elements -->
    <div
        class="absolute top-20 left-10 w-20 h-20 bg-indigo-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob">
    </div>
    <div
        class="absolute top-40 right-10 w-20 h-20 bg-cyan-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000">
    </div>
    <div
        class="absolute bottom-20 left-20 w-20 h-20 bg-pink-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000">
    </div>

    <!-- Logo Section -->
    <div class="mb-8 transform hover:scale-105 transition-transform duration-300">
        {{ $logo }}
    </div>

    <!-- Main Card -->
    <div
        class="w-full sm:max-w-lg mx-4 bg-white/80 backdrop-blur-sm shadow-2xl border border-white/20 overflow-hidden sm:rounded-2xl relative">
        <!-- Card Header Gradient -->
        <div class="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-indigo-500 via-purple-500 to-cyan-500">
        </div>

        <!-- Card Content -->
        <div class="px-8 py-8 sm:px-10 sm:py-10">
            {{ $slot }}
        </div>
    </div>

    <!-- Footer -->
    <div class="mt-8 text-center">

    </div>
</div>

<style>
    @keyframes blob {
        0% {
            transform: translate(0px, 0px) scale(1);
        }

        33% {
            transform: translate(30px, -50px) scale(1.1);
        }

        66% {
            transform: translate(-20px, 20px) scale(0.9);
        }

        100% {
            transform: translate(0px, 0px) scale(1);
        }
    }

    .animate-blob {
        animation: blob 7s infinite;
    }

    .animation-delay-2000 {
        animation-delay: 2s;
    }

    .animation-delay-4000 {
        animation-delay: 4s;
    }

    .bg-grid-slate-100 {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' width='32' height='32' fill='none' stroke='rgb(148 163 184 / 0.05)'%3e%3cpath d='m0 .5h32m-32 32v-32'/%3e%3c/svg%3e");
    }
</style>