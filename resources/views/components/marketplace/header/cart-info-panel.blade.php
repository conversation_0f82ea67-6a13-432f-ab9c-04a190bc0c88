{{-- @if (Request::is('marketplace*')) --}}


@php
    // detemine cart initial visibility
    $cartPreview = $cartPreview ?? 'false';
@endphp

{{-- CART ICON --}}
    <div @click="cartPreview = ! cartPreview" id="menu-cart-section"
         class="relative inline-flex items-center p-2 text-sm font-medium text-center text-white bg-gray-700 rounded-full hover:bg-gray-900 focus:ring-4 focus:outline-none focus:ring-gray-300
        dark:bg-gray-700 dark:hover:bg-gray-700 dark:focus:ring-gray-800 cursor-pointer"

         x-data="{cartPreview: {{$cartPreview}} }">


        <div>
            {{-- icon --}}
            <x-icons.etc.cart class="w-4 h-4 md:w-5 md:h-5"/>


            <span class="sr-only">Cart</span>

            <div id="menu-cart-icon-count"
                 class="absolute inline-flex items-center justify-center
                w-6 h-6 text-xs font-medium text-white bg-orange-500 border-2 border-white 
                rounded-full -top-2 -right-2 dark:border-orange-500
                transition duration-150 ease-in-out">

                {{-- items in cart count --}}
                {{ count($cartItems) }}

            </div>
        </div>


        {{-- Cart Items Preview Section --}}

        @if(count($cartItems) > 0)
            <div style="
      margin-top: 10px;
      right: 25px;
      top: 30px;
      min-width: max-content;
      will-change: transform;
      z-index: 100;"

                 {{-- x-bind:class="cartPreview ? 'opacity-100' : 'opacity-0'" --}}

                 class="absolute left-[-100px] bg-white inset-x-0  mt-px pb-6 shadow-lg sm:px-2 lg:left-auto lg:right-0 lg:top-full lg:-mr-1.5  lg:mt-3 lg:w-80 lg:rounded-lg lg:ring-1 lg:ring-black lg:ring-opacity-5 transition-all z-30"

                 @click.away="cartPreview=false"
                 x-transition:enter="ease-out duration-200"
                 x-transition:enter-start="-translate-y-2"
                 x-transition:enter-end="translate-y-0"

                 x-show="cartPreview"
                 x-cloak>


                <div class="mx-auto max-w-2xl px-4 text-left">

                    <ul role="list" class="divide-y divide-gray-200 pt-2">

                        @foreach($cartItems as $item)

                            <li class="flex items-center py-3 text-sm">
                                <img src="https://www.google.com/s2/favicons?sz=64&domain_url={{$item['website']}}"
                                     alt="favicon" class="h-5 flex-none rounded-md border border-gray-200">
                                <div class="ml-4 flex-auto">
                                    <h3 class="font-medium text-gray-900">
                                        <a href="#">{{$item['website']}}</a>
                                    </h3>
                                </div>
                            </li>

                        @endforeach

                    </ul>


                    <a href="{{route('cart')}}" wire:navigate class="z-50">
                        <button type="submit"
                                class="mt-4 w-full rounded-md border border-transparent bg-gray-900 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-800 focus:ring-offset-2 focus:ring-offset-gray-50">
                            Checkout
                        </button>
                    </a>

                    {{-- <a href="{{route('cart')}}" class="text-xs font-medium text-muted-foreground hover:text-primary">
                      <p class="mt-4 text-center">Review Your Order</p>
                    </a> --}}

                </div>

            </div>

        @endif

    </div>


    {{-- @endif --}}