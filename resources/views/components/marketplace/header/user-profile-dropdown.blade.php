<div x-data="{ profileDropDown: false }" class="profile-section-right">


    {{-- Avatar --}}
    <span id="avatar-top" class="cursor-pointer relative flex shrink-0 overflow-hidden rounded-full h-10 w-10">
        <div @click="profileDropDown = ! profileDropDown"
            class="aspect-square h-full w-full flex items-center justify-center bg-gray-200 hover:bg-gray-300 transition-colors">
            @if (auth()->user()->profile_photo_path)
            <img src="{{ Storage::url(auth()->user()->profile_photo_path) }}" alt="Profile Photo"
                class="w-full rounded-full object-cover">
            @else
            <x-icons.etc.profile class="w-6 h-6 text-gray-600" />
            @endif
        </div>
    </span>


    {{-- Drop down --}}
    <div x-show="profileDropDown" @click.away="profileDropDown=false" x-transition:enter="ease-out duration-200"
        x-transition:enter-start="-translate-y-2" x-transition:enter-end="translate-y-0" x-cloak class="opacity-0"
        x-bind:class="profileDropDown ? 'opacity-100' : 'opacity-0'" style="
          position: absolute;
          background: white;
          margin-top: 5px;
          right: 20px;
        /*          transform: translate(1323px, 192px);*/
          min-width: max-content;
          will-change: transform;
          z-index: 50;">


        <div role="menu" aria-orientation="vertical"
            class="bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-56 min-w-[8rem] overflow-hidden rounded-md border p-1 shadow-md "
            tabindex="-1" data-orientation="vertical" style="
                outline: none;
                pointer-events: auto;">



            {{-- User info --}}
            <div class="px-2 pt-3 pb-1 text-sm font-normal">
                <div class="flex flex-col space-y-1 ps-1">
                    <p class="text-sm font-medium leading-none">
                        {{ auth()->user()->name }}
                    </p>
                    <p class="text-muted-foreground text-xs leading-none">
                        {{ auth()->user()->email }}
                    </p>
                </div>
            </div>


            {{-- Links --}}
            <div role="separator" aria-orientation="horizontal" class="bg-muted -mx-1 my-2.5 h-px"></div>
            <div role="group">
                {{-- No separate dashboard link, we will auto redirect when they enter platform --}}
                @can('admin')
                <a href="{{ route('admin.dashboard') }}" wire:navigate role="menuitem"
                    class="hover:bg-accent cursor-pointer focus:bg-accent focus:text-accent-foreground relative flex  select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors data-[disabled]:pointer-events-none data-[disabled]:opacity-50"
                    tabindex="-1" data-orientation="vertical" data-radix-collection-item="">
                    <x-icons.etc.gear class="w-4 h-4 mr-1" />
                    Admin Dashboard
                </a>
                @endcan


                @can('advertiser')
                <a href="{{ route('advertiser.dashboard') }}" wire:navigate role="menuitem"
                    class="hover:bg-accent cursor-pointer focus:bg-accent focus:text-accent-foreground relative flex  select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors data-[disabled]:pointer-events-none data-[disabled]:opacity-50"
                    tabindex="-1" data-orientation="vertical" data-radix-collection-item="">
                    <x-icons.etc.gear class="w-4 h-4 mr-1" />
                    Advertiser Dashboard
                </a>
                @endcan


                @can('publisher')
                <a href="{{ route('publisher.dashboard') }}" wire:navigate role="menuitem"
                    class="hover:bg-accent cursor-pointer focus:bg-accent focus:text-accent-foreground relative flex  select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors data-[disabled]:pointer-events-none data-[disabled]:opacity-50"
                    tabindex="-1" data-orientation="vertical" data-radix-collection-item="">
                    <x-icons.etc.gear class="w-4 h-4 mr-1" />
                    Publisher Dashboard
                </a>
                @endcan

                @can('writer')
                    <a href="{{ route('admin.writer.dashboard') }}" wire:navigate role="menuitem"
                        class="hover:bg-accent cursor-pointer focus:bg-accent focus:text-accent-foreground relative flex  select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors data-[disabled]:pointer-events-none data-[disabled]:opacity-50"
                        tabindex="-1" data-orientation="vertical" data-radix-collection-item="">
                        <x-icons.etc.gear class="w-4 h-4 mr-1" />
                        Writer Dashboard
                        </a>
                @endcan


                <a href="{{ route('profile.show') }}" wire:navigate role="menuitem"
                    class="hover:bg-accent cursor-pointer focus:bg-accent focus:text-accent-foreground relative flex  select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors data-[disabled]:pointer-events-none data-[disabled]:opacity-50"
                    tabindex="-1" data-orientation="vertical" data-radix-collection-item="">
                    <x-icons.etc.profile class="w-4 h-4 mr-1" />
                    Profile
                    {{-- <span class="ml-auto text-xs tracking-widest opacity-60">⇧⌘P</span> --}}
                </a>

                <a href="mailto:{{ env('SUPPORT_EMAIL') }}" {{-- wire:navigate --}} role="menuitem"
                    class="hover:bg-accent cursor-pointer focus:bg-accent focus:text-accent-foreground relative flex  select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors data-[disabled]:pointer-events-none data-[disabled]:opacity-50"
                    tabindex="-1" data-orientation="vertical">
                    <x-icons.etc.support class="w-4 h-4 mr-1" />
                    Support
                    {{-- <span class="ml-auto text-xs tracking-widest opacity-60">⌘S</span> --}}
                </a>
            </div>


            <div role="separator" aria-orientation="horizontal" class="bg-muted -mx-1 my-1.5 h-px"></div>


            {{-- logout --}}
            <form method="POST" action="{{ route('logout') }}">
                @CSRF
                <button class="w-full" type="submit">
                    <div role="menuitem"
                        class=" focus:bg-accent hover:bg-accent focus:text-accent-foreground relative flex  cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors"
                        tabindex="-1" data-orientation="vertical" data-radix-collection-item="">
                        <x-icons.etc.logout class="w-4 h-4 mr-1" />
                        Log out
                    </div>
                </button>
            </form>

        </div>
    </div>

</div>