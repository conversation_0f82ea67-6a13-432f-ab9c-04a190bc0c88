<template>
    <!-- HEADER -->

    <div
        class="sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8">
        <button type="button" @click="$emit('open-sidebar')" class="-m-2.5 p-2.5 text-gray-700 lg:hidden">
            <span class="sr-only">Open sidebar</span>
            <Bars3Icon class="size-6" aria-hidden="true" />
        </button>

        <!-- Separator -->
        <div class="h-6 w-px bg-gray-900/10 lg:hidden" aria-hidden="true" />

        <div class="flex flex-1 gap-x-4 self-stretch lg:gap-x-6  justify-end">

            <div class="flex items-center gap-x-4 lg:gap-x-7">
                <template v-if="user">
                    <button @click="goToWallet"
                        v-if="user.role != page.props.roleEnums.Outreach && user.role != page.props.roleEnums.Writer"
                        type="button" class="-m-2.5 p-2.5 text-gray-600 hover:text-gray-500">
                        <span class="sr-only">View Wallet</span>
                        <div class="flex gap-1 items-center">

                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="1.7" stroke-linecap="round"
                                stroke-linejoin="round" class="size-5 text-gray-400 lucide-wallet">
                                <path
                                    d="M19 7V4a1 1 0 0 0-1-1H5a2 2 0 0 0 0 4h15a1 1 0 0 1 1 1v4h-3a2 2 0 0 0 0 4h3a1 1 0 0 0 1-1v-2a1 1 0 0 0-1-1" />
                                <path d="M3 5v14a2 2 0 0 0 2 2h15a1 1 0 0 0 1-1v-4" />
                            </svg>

                            <!-- <CurrencyDollarIcon class="size-5 text-gray-400" aria-hidden="true" /> -->

                            <!-- <WalletIcon class="size-5 text-gray-400" aria-hidden="true" /> -->
                            <span class="text-gray-400 text-[17px] font-semibold">
                                <span class="-mr-1">$</span>

                                {{ page.props.superAdminWalletBalance }}
                            </span>
                        </div>
                    </button>


                    <!-- Profile dropdown -->
                    <Menu as="div" class="relative" v-if="user.role !== page.props.roleEnums.Advertiser">
                        <MenuButton class="-m-1.5 flex items-center p-1.5">
                            <div class="-m-2.5 p-2.5 text-gray-400 hover:text-gray-500 relative">
                                <span class="sr-only">View notifications</span>
                                <BellIcon :class="[
                                    'size-6',
                                    unreadCount > 0 && animateBell ? 'animate-shake-twice' : ''
                                ]" aria-hidden="true" />
                                <span v-if="unreadCount > 0"
                                    class="absolute -top-0 -right-0 inline-flex items-center justify-center rounded-full bg-red-500 px-1.5 text-xs text-white">
                                    {{ unreadCount }}
                                </span>
                            </div>
                        </MenuButton>
                        <transition enter-active-class="transition ease-out duration-100"
                            enter-from-class="transform opacity-0 scale-95"
                            enter-to-class="transform opacity-100 scale-100"
                            leave-active-class="transition ease-in duration-75"
                            leave-from-class="transform opacity-100 scale-100"
                            leave-to-class="transform opacity-0 scale-95">

                            <MenuItems
                                class="w-[250px] absolute right-0 z-10 mt-2.5  origin-top-right rounded-md bg-white py-2 shadow-lg ring-1 ring-gray-900/5 focus:outline-none">
                                <div class="flex justify-between items-center border-b py-1 px-3">
                                    <div class="text-center">Notifications</div>
                                    <button v-if="unreadCount > 0" @click="markAllAsRead"
                                        class="text-xs text-blue-600 hover:text-blue-800">
                                        Mark all as read
                                    </button>
                                </div>

                                <div class="max-h-[300px] overflow-y-auto">
                                    <MenuItem v-if="messages.length > 0" v-for="message in messages"
                                        :key="message.message" v-slot="{ active }">
                                    <a @click="markNotificationAsRead(message.id)" :class="[
                                        active ? 'bg-gray-100 outline-none' : '',
                                        !message.read_at ? 'bg-gray-50 font-medium' : '',
                                        'block px-3 py-2 border-b text-sm/6 cursor-pointer hover:bg-gray-100'
                                    ]">
                                        <div class="flex items-center gap-2">
                                            <component :is="message.icon" class="size-4 shrink-0" aria-hidden="true" />
                                            <span :class="!message.read_at ? 'text-gray-900' : 'text-gray-600'">
                                                {{ message.message }}
                                            </span>
                                        </div>
                                    </a>
                                    </MenuItem>
                                    <MenuItem v-else>
                                    <div class="flex flex-col justify-center items-center">
                                        <Drawer />
                                        <div>
                                            No Notifications
                                        </div>
                                    </div>
                                    </MenuItem>
                                </div>
                            </MenuItems>
                        </transition>
                    </Menu>
                    <!-- Separator -->
                    <div class="hidden lg:block lg:h-6 lg:w-px lg:bg-gray-900/10" aria-hidden="true" />

                    <!-- dropdown -->
                    <Menu as="div" class="relative">
                        <MenuButton class="-m-1.5 flex items-center p-1.5">
                            <span class="sr-only">Open user menu</span>
                            <img class="size-7 rounded-full bg-gray-50" :class="!profile_photo_path ? 'opacity-60' : ''"
                                :src="profile_photo_path ? profile_photo_path : `/assets/avatars/man.svg`" alt="" />
                            <span class="hidden lg:flex lg:items-center">
                                <span class="ml-2 text-sm/6 font-semibold text-gray-900" aria-hidden="true">
                                    {{ user?.name || 'User' }}
                                </span>
                                <ChevronDownIcon class="ml-1 size-5 text-gray-400" aria-hidden="true" />
                            </span>
                        </MenuButton>
                        <transition enter-active-class="transition ease-out duration-100"
                            enter-from-class="transform opacity-0 scale-95"
                            enter-to-class="transform opacity-100 scale-100"
                            leave-active-class="transition ease-in duration-75"
                            leave-from-class="transform opacity-100 scale-100"
                            leave-to-class="transform opacity-0 scale-95">
                            <MenuItems
                                class="absolute border flex flex-col right-0 z-10 mt-2.5  origin-top-right rounded-md bg-white py-2 shadow-lg ring-1 ring-gray-900/5 focus:outline-none">

                                <MenuItem>
                                <a href="/marketplace" role="button"
                                    class="border-b cursor-pointer block px-3 py-2 text-sm/6 text-gray-900 hover:bg-gray-100">
                                    <div class="flex items-center gap-2 whitespace-nowrap ">
                                        <component :is="Store" class="size-4 shrink-0" aria-hidden="true" />
                                        Go To Marketplace
                                    </div>
                                </a>
                                </MenuItem>
                                <MenuItem v-for="item in userNavigation" :key="item.name" v-slot="{ active }">
                                <a :href="route(item.href)" class="border-b"
                                    :class="[active ? 'bg-gray-100 outline-none' : '', 'block px-3 py-2 text-sm/6 text-gray-900']">
                                    <div class="flex items-center gap-2">
                                        <component :is="item.icon" class="size-4 shrink-0" aria-hidden="true" />
                                        {{ item.name }}
                                    </div>
                                </a>
                                </MenuItem>

                                <MenuItem>
                                <a @click.prevent="logout" role="button"
                                    class="cursor-pointer block px-3 py-2 text-sm/6 text-gray-900 hover:bg-gray-100">
                                    <div class="flex items-center gap-2">
                                        <component :is="LogOut" class="size-4 shrink-0" aria-hidden="true" />
                                        Logout
                                    </div>
                                </a>
                                </MenuItem>
                            </MenuItems>
                        </transition>
                    </Menu>
                </template>
                <template v-else>
                    <a :href="route('login')"
                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Login
                    </a>
                </template>
            </div>
        </div>
    </div>
    <!-- HEADER -->
</template>


<script setup>
// ================================
// Imports
// ================================

import {
    Menu,
    MenuButton,
    MenuItem,
    MenuItems,
} from '@headlessui/vue'


import { ChevronDownIcon } from '@heroicons/vue/20/solid'
import { LogOut, User, Store, MessageSquare } from 'lucide-vue-next'
import axios from "axios"
import { inject, ref, computed, onMounted } from "vue"
import { router, usePage } from '@inertiajs/vue3'
import Drawer from "@/Components/Icons/Drawer.vue"


import {
    Bars3Icon,
    BellIcon,
    CurrencyDollarIcon,
    WalletIcon
} from '@heroicons/vue/24/outline'


// ================================
// Props & Page Context
// ================================

const page = inject("page")
const user = page.props.auth?.user
const profile_photo_path = user?.profile_photo_path
const notify = inject("$notify")

const animateBell = ref(false);  // Controls the CSS class

// ================================
// Emits
// ================================

defineEmits(['open-sidebar'])


const startAnimation = () => {
    animateBell.value = true;    // Start the animation
    setTimeout(() => {
        animateBell.value = false;  // Remove the class after animation completes
    }, 2000);  // Match your CSS animation duration
};


// ================================
// On Mount Logic
// ================================
onMounted(() => {
    if (unreadCount.value > 0) {
        startAnimation();  // Animate on page load if unreadCount > 0
    }

    setInterval(() => {
        if (unreadCount.value > 0) {   // Only animate if there are unread notifications
            startAnimation();
        }
    }, 10000); // Animate every 10 seconds
});





// ================================
// User Navigation Menu Items
// ================================

const userNavigation = [
    { name: 'Your profile', href: 'profile.show', icon: User },
]





// ================================
// Notifications Logic
// ================================

// Set initial messages from props
const messages = ref([...page.props.notifications ?? []])

// Count unread notifications
const unreadCount = computed(() => {
    return messages.value.filter(msg => !msg.read_at).length
})





// ================================
// Mark Individual Notification as Read Logic
// ================================
const markNotificationAsRead = async (notificationId) => {
    try {
        await axios.post(route('notifications.markAsRead', { id: notificationId }))

        const notification = messages.value.find(msg => msg.id === notificationId)
        if (notification) {
            notification.read_at = new Date()

            if (notification.href) {
                router.visit(notification.href, {
                    preserveScroll: true,
                    preserveState: false,
                })
            }
        }
    } catch (error) {
        console.error('Failed to mark notification as read:', error)
    }
}





// ================================
// Mark All Notifications as Read Logic
// ================================
const markAllAsRead = async () => {
    try {
        await axios.post(route('notifications.markAllAsRead'))
        messages.value.forEach(msg => {
            msg.read_at = new Date()
        })
    } catch (error) {
        console.error('Failed to mark all notifications as read:', error)
    }
}




// ================================
// Real-time Notification Listener
// ================================
onMounted(() => {
    const userId = usePage().props.auth?.user?.id

    if (userId) {
        Echo.private(`App.Models.User.${userId}`)
            .notification((notification) => {
                messages.value.unshift({
                    id: notification.id,
                    message: notification.message,
                    icon: 'MessageSquare',
                    href: notification.href,
                    read_at: null
                })

                if (messages.value.length > 10) {
                    messages.value.pop()
                }

                notify(notification.message, { type: "info" })
            })
    }
})





// ================================
// Logout Logic
// ================================

const logout = async () => {
    try {
        await axios.post(route("logout"))
        window.location.href = route("login") // Full reload
    } catch (error) {
        console.error("Logout failed:", error)
    }
}




// ================================
// Go to Wallet Logic
// ================================
const goToWallet = () => {
    const role = usePage().props.auth?.user?.role;
    let wallet_route;
    if (role === 'admin' || role === 'superadmin') {
        wallet_route = 'admin.wallet.index';
    } else if (role === 'publisher') {
        wallet_route = 'publisher.wallet.index';
    } else if (role === 'advertiser') {
        // wallet_route = 'advertiser.wallet.index';
    }
    if (wallet_route) {
        router.visit(route(wallet_route));
    }
}


</script>
