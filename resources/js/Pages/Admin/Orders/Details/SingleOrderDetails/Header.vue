<template>
    <div>
        <div class="flex flex-col gap-3 mt-5 border border-gray-200 p-1 sm:p-5 bg-gray-100">
            <div class="border border-gray-200 bg-white">
                <div class=" p-4">
                    <div class="flex items-center w-full gap-4">
                        <h3 class="text-base font-semibold text-gray-900 whitespace-nowrap">Details</h3>

                        <div class="flex-grow border-t border-dashed border-gray-300"></div>

                        <Listbox v-model="selectedState" as="div" class="w-auto">
                            <div class="relative">
                                <ListboxButton
                                    class="inline-flex items-center justify-between gap-x-2 rounded-md w-full bg-gray-600 px-4 py-4 text-white hover:bg-gray-700 hover:text-white focus:outline-none"
                                    :class="getStatusClass(item.state_name) + ' transition-all'">
                                    <div class="inline-flex items-center gap-x-2 my-3 px-3 pe-5">
                                        <Check class="size-5" aria-hidden="true" />
                                        <p class=" text-lg font-semibold truncate text-wrap">{{ selectedState }}</p>
                                    </div>
                                    <ChevronDownIcon class="hidden size-5 text-white shrink-0" aria-hidden="true" />
                                </ListboxButton>
                                <transition leave-active-class="transition ease-in duration-100"
                                    leave-from-class="opacity-100" leave-to-class="opacity-0">
                                    <ListboxOptions
                                        class="absolute z-10 mt-2 w-full origin-top-right overflow-hidden rounded-md bg-white shadow-lg ring-1 ring-black/5 focus:outline-none">
                                        <ListboxOption as="template"
                                            v-for="transition in [{ label: item.state_label, class: item.state }, ...item.possible_transitions]"
                                            :key="transition.class" :value="transition.label"
                                            v-slot="{ active, selected }">
                                            <li
                                                :class="[active ? 'bg-gray-600 text-white' : 'text-gray-900', 'cursor-default select-none p-4 text-sm']">
                                                <div class="flex justify-between items-center">
                                                    <p :class="selected ? 'font-semibold' : 'font-normal'">{{
                                                        transition.label }}</p>
                                                    <Check v-if="selected" class="size-5"
                                                        :class="active ? 'text-white' : 'text-gray-600'"
                                                        aria-hidden="true" />
                                                </div>
                                            </li>
                                        </ListboxOption>
                                    </ListboxOptions>
                                </transition>
                            </div>
                        </Listbox>
                    </div>
                    <div class="flex flex-row">
                        <div class="mt-5 flex-1">
                            <div class="flex gap-x-2 border-b border-1 border-gray-100">
                                <dt class="text-sm/6 font-medium text-gray-900">Order ID:</dt>
                                <dd
                                    class="flex flex-row items-center gap-2 mt-1 text-sm/6 text-gray-700 sm:col-span-2 sm:mt-0">
                                    <span class="">
                                        #{{ order.id }},
                                    </span>
                                    <div>
                                        <span class="text-sm/6 font-medium text-gray-900">Item ID: </span>
                                        <span>{{ item.id }}</span>
                                    </div>

                                </dd>
                            </div>
                            <!-- <div class="flex gap-x-2 border-b border-1 border-gray-100">
                                <dt class="text-sm/6 font-medium text-gray-900">Time Elapsed:</dt>
                                <dd class="mt-1 text-sm/6 text-gray-700 sm:col-span-2 sm:mt-0">{{ item.time_elapsed }}
                                </dd>
                            </div> -->
                            <div class="flex gap-x-2 border-b border-1 border-gray-100">
                                <dt class="text-sm/6 font-medium text-gray-900">Niche:</dt>
                                <dd class="mt-1 text-sm/6 text-gray-700 sm:col-span-2 sm:mt-0">
                                    <span class="">
                                        {{ item.niche }}
                                    </span>
                                </dd>
                            </div>
                            <div class="flex gap-x-2 border-b border-1 border-gray-100">
                                <dt class="text-sm/6 font-medium text-gray-900">Earning:</dt>
                                <dd class="mt-1 text-sm/6 text-gray-700 sm:col-span-2 sm:mt-0">
                                    <span class="">
                                        ${{ item.price_paid }}
                                    </span>
                                </dd>
                            </div>
                            <div class="flex gap-x-2 border-b border-1 border-gray-100">
                                <dt class="text-sm/6 font-medium text-gray-900">Website:</dt>
                                <dd
                                    class="flex flex-row items-center gap-2 mt-1 text-sm/6 text-gray-700 sm:col-span-2 sm:mt-0">
                                    <span class="">
                                        {{ item.website.website_domain }}
                                    </span>
                                    <a :href="returnWebsiteUrl(item.website)" target="_blank" class="text-indigo-500">
                                        <ExternalLink class="w-4 h-4" />
                                    </a>
                                </dd>
                            </div>
                        </div>

                        <div class="mt-5 flex-1">
                            <!-- <div class="flex gap-x-2 border-b border-1 border-gray-100">
                                <dt class="text-sm/6 font-medium text-gray-900">Due Date:</dt>

                                <dd class="mt-1 text-sm/6 text-gray-700 sm:col-span-2 sm:mt-0">
                                    {{ item.estimated_publication_date_formatted ?? 'Date not found'  }}
                                </dd>
                            </div> -->
                            <div class="flex gap-x-2 border-b border-1 border-gray-100">
                                <dt class="text-sm/6 font-medium text-gray-900">Last Updated:</dt>
                                <dd class="mt-1 text-sm/6 text-gray-700 sm:col-span-2 sm:mt-0">
                                    <span class="">
                                        6 hours ago
                                    </span>
                                </dd>
                            </div>
                            <div class="flex gap-x-2 border-b border-1 border-gray-100">
                                <dt class="text-sm/6 font-medium text-gray-900">Order Date:</dt>
                                <dd class="mt-1 text-sm/6 text-gray-700 sm:col-span-2 sm:mt-0">
                                    <span class="">
                                        {{ order.created_at_formatted }}
                                    </span>
                                </dd>
                            </div>

                            <div class="flex gap-x-2 border-b border-1 border-gray-100">
                                <dt class="text-sm/6 font-medium text-gray-900">Content Provided By:</dt>
                                <dd class="mt-1 text-sm/6 text-gray-700 sm:col-span-2 sm:mt-0">
                                    <span :class="[
                                        'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                                        item.is_content_provided_by_customer == 1
                                            ? 'bg-blue-100 text-blue-800'
                                            : 'bg-green-100 text-green-800'
                                    ]">
                                        {{ item.is_content_provided_by_customer == 1 ? 'Advertiser' : 'Writer' }}
                                    </span>
                                </dd>
                            </div>


                            <div class="flex gap-x-2 border-b border-1 border-gray-100">
                                <dt class="text-sm/6 font-medium text-gray-900 whitespace-nowrap">Published URL:</dt>
                                <dd class="mt-1 text-sm/6 text-gray-700 sm:col-span-2 sm:mt-0 inline-flex">

                                    <a v-if="item.publication?.publication_url"
                                        :href="item.publication?.publication_url" target="_blank"
                                        class="text-indigo-500">
                                        <span class="inline-flex items-center gap-2">
                                            {{ item.publication?.publication_url ? item.publication?.publication_url :
                                                'Not published yet' }}
                                        </span>
                                        <!-- <ExternalLink class="w-4 h-4" /> -->
                                    </a>
                                </dd>
                            </div>

                            <div v-if="item.is_content_provided_by_customer == 0"
                                class="flex items-center gap-x-2 border-b border-1 border-gray-100">
                                <dt class="text-sm/6 font-medium text-gray-900">Content Status:</dt>
                                <dd class="mt-1 text-sm/6 text-gray-700 sm:col-span-2 sm:mt-0">
                                    <div v-if="item.is_content_provided_by_customer == 0"
                                        class="flex flex-row items-center gap-2">
                                        <ContentStatus :status="item.content_status" />

                                        <div class="flex flex-row items-center gap-2 text-xs">
                                            <div v-if="item.content" class="text-gray-500">
                                                <span class="font-bold">Writer: </span>
                                                <span>{{ item.content?.writer?.name }}</span>
                                            </div>
                                            <div v-if="item.content_status !== 'Completed'">
                                                <Link :href="route('admin.orders.assignments.edit', { item: item.id })"
                                                    class="text-indigo-500 flex flex-row items-center gap-1">
                                                <UserPlus class="w-4 h-4" />
                                                {{ item.content?.writer ? 'Manage Writer' : 'Assign Writer' }}
                                                </Link>
                                            </div>
                                        </div>
                                    </div>
                                </dd>
                            </div>
                            <!-- <div>
                                {{ item }}
                            </div> -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
// ==========================================================
// Imports
// ==========================================================
import { ref, inject, watch } from 'vue'
import { router } from '@inertiajs/vue3'
import { Listbox, ListboxButton, ListboxOption, ListboxOptions } from '@headlessui/vue'
import { Check, ChevronDown as ChevronDownIcon, ExternalLink, UserPlus } from 'lucide-vue-next'
import { getStatusClass } from '@/helpers/utils.js'
import ContentStatus from '@/Components/ContentStatus.vue'
import { returnWebsiteUrl } from '@/lib/utils';

// ==========================================================
// Props
// ==========================================================
const props = defineProps({
    order: Object,
    item: Object,
})

// ==========================================================
// Refs & Injects
// ==========================================================
const notify = inject('$notify')
const selectedState = ref(props.item.state_label)
const previousState = ref(props.item.state_label) // Track previous state to revert on cancel



// ==========================================================
// Methods - Handle State Transition
// Change order item's state using a confirmed PUT request
// ==========================================================
const changeState = (label) => {
    const selectedTransition = props.item.possible_transitions.find(t => t.label === label)
    if (!selectedTransition) return

    if (confirm('Are you sure you want to change the state?')) {
        router.put(
            route('admin.orders.item.update-state', { item: props.item.id }),
            { state: selectedTransition.class },
            {
                preserveScroll: true,
                onSuccess: () => {
                    notify('Order item state updated', { type: 'success' })
                    previousState.value = label
                    router.reload({ only: ['item'], preserveScroll: true })
                },
                onError: (error) => {
                    notify(error.message, { type: 'error' })
                    // previousState.value = label
                    selectedState.value = previousState.value

                    router.reload({ only: ['item'], preserveScroll: true })
                }
            }
        )
    } else {
        // Revert if user cancels
        selectedState.value = previousState.value

    }
}

// ==========================================================
// Watchers
// ==========================================================
// Watch selectedState and trigger state change when updated
watch(selectedState, changeState)

</script>


<style scoped></style>