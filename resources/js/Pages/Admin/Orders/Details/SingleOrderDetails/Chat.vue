<template>
    <div class="flex flex-col h-full">
        <!-- Chat Display -->
        <div ref="chatContainer"
            class="p-4 pt-0 border rounded-t-xl bg-white flex flex-col gap-3 max-h-[400px]  min-h-[400px] overflow-y-auto relative">
            <div class="sticky pt-4 top-0 bg-white z-10 -mt-4 -mx-4 px-4 pb-2 border-b">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-800">Chat</h3>
                </div>
            </div>
            <!-- Messages Container -->
            <div v-if="messages.length === 0" class="flex-1 flex items-center justify-center min-h-[200px]">
                <div class="text-center text-gray-500">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto mb-2" fill="none"
                        viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                    <p class="text-sm">No messages yet</p>
                    <p class="text-xs mt-1">Start the conversation</p>
                </div>
            </div>
            <div v-else v-for="message in messages" :key="message.id" :data-message-id="message.id"
                class="flex gap-2 mt-4" :class="message.sender_id === senderId ? 'justify-end' : 'justify-start'">
                <!-- Profile photo for other users -->
                <div v-if="message.sender_id !== senderId" class="flex-shrink-0">
                    <img :src="message.sender?.profile_photo_path ? '/storage/' + message.sender?.profile_photo_path : '/assets/avatars/man.svg'"
                        :alt="message.sender?.name || 'User'" class="w-8 h-8 rounded-full object-cover">
                </div>

                <!-- Message content -->
                <div class="flex flex-col" :class="message.sender_id === senderId ? 'items-end' : 'items-start'">
                    <div :class="message.sender_id === senderId
                        ? 'bg-blue-500 text-white'
                        : 'bg-gray-100 text-gray-900'" class="p-3 rounded-xl w-fit max-w-[75%]">
                        <div v-if="message.text" class="whitespace-pre-line">{{ message.text }}</div>
                        <div v-if="message.attachment_path" class="mt-2">
                            <div v-if="isImageFile(message.attachment_path)" class="relative group">
                                <img :src="'/storage/' + message.attachment_path" alt="Chat image"
                                    class="max-w-full max-h-64 rounded-lg cursor-pointer hover:opacity-90 transition-opacity"
                                    @click="openImageModal('/storage/' + message.attachment_path)">
                                <div
                                    class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all rounded-lg flex items-center justify-center">
                                    <svg class="w-6 h-6 text-white opacity-0 group-hover:opacity-100 transition-opacity"
                                        fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7">
                                        </path>
                                    </svg>
                                </div>
                            </div>
                            <a v-else :href="'/storage/' + message.attachment_path" target="_blank"
                                :class="message.sender_id === senderId ? 'text-blue-200' : 'text-blue-600'"
                                class="inline-flex items-center gap-2 text-sm hover:underline">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24"
                                    stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                                </svg>
                                View Attachment
                            </a>
                        </div>
                    </div>

                    <!-- Message metadata -->
                    <div class="flex items-center gap-1 mt-1">
                        <span v-if="message.sender_id !== senderId" class="text-xs text-gray-500">{{
                            message.sender?.name || 'User' }}</span>
                        <span class="text-xs opacity-70">{{ formatTime(message.created_at) }}</span>
                    </div>
                </div>

                <!-- Profile photo for current user -->
                <div v-if="message.sender_id === senderId" class="flex-shrink-0">
                    <img :src="message.sender?.profile_photo_path ? '/storage/' + message.sender?.profile_photo_path : '/assets/avatars/man.svg'"
                        :alt="message.sender?.name || 'User'" class="w-8 h-8 rounded-full object-cover">
                </div>
            </div>
        </div>

    </div>
</template>


<script setup>
import { ref, onMounted } from 'vue'
import axios from 'axios'
import { formatDistanceToNow } from 'date-fns'

const props = defineProps({
    item: {
        type: Object,
        required: true
    },
    order: {
        type: Object,
        required: true
    }
});

const messages = ref([])
const chatContainer = ref(null)
const orderItemId = ref(props.item.id)
const hasNewMessages = ref(false)
const senderId = ref(props.order.user_id)

const fetchMessages = async () => {
    const { data } = await axios.get(route('messages.index', orderItemId.value))
    messages.value = data
    hasNewMessages.value = false
}


const formatTime = (date) => {
    return formatDistanceToNow(new Date(date), { addSuffix: true })
}

const isImageFile = (filePath) => {
    const extension = filePath.split('.').pop().toLowerCase();
    return ['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(extension);
}

const openImageModal = (imageSrc) => {
    // Create modal element
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75';
    modal.style.display = 'flex';

    const modalContent = document.createElement('div');
    modalContent.className = 'relative max-w-4xl max-h-full p-4';

    const closeButton = document.createElement('button');
    closeButton.className = 'absolute top-2 right-2 text-white hover:text-gray-300 transition-colors z-10';
    closeButton.innerHTML = `
        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
    `;
    closeButton.onclick = () => document.body.removeChild(modal);

    const image = document.createElement('img');
    image.src = imageSrc;
    image.alt = 'Full size image';
    image.className = 'max-w-full max-h-full object-contain rounded-lg';

    modalContent.appendChild(closeButton);
    modalContent.appendChild(image);
    modal.appendChild(modalContent);
    document.body.appendChild(modal);

    // Close on escape key
    const handleEscape = (e) => {
        if (e.key === 'Escape') {
            document.body.removeChild(modal);
            document.removeEventListener('keydown', handleEscape);
        }
    };
    document.addEventListener('keydown', handleEscape);

    // Close on background click
    modal.onclick = (e) => {
        if (e.target === modal) {
            document.body.removeChild(modal);
            document.removeEventListener('keydown', handleEscape);
        }
    };
}

onMounted(() => {
    fetchMessages();

})




</script>

<style scoped></style>
