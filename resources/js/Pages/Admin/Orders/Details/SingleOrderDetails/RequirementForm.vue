<template>
    <div>
        <!-- Header with Edit button -->
        <div class="flex justify-between mb-5">

            <div class="flex flex-row gap-2 items-center">
                <h3 class="text-base/7 font-semibold text-gray-900">Requirement Details</h3>
                <div v-if="item.requirements?.advertiser_url">
                    <Check class="w-4 h-4 text-green-500" />
                </div>
            </div>


            <button class="text-blue-500 text-sm px-3 py-1 rounded-md flex flex-row gap-1 items-center" @click="startEdit" v-if="!showEdit">
                <Pencil class="w-4 h-4" />
                <span>Edit Requirements</span>

            </button>
        </div>

        <!-- View Mode -->
        <div class="flex flex-col gap-2" v-show="!showEdit">
            <div class="flex gap-x-3 border-b border-gray-100 items-center">
                <dt class="text-sm/6 font-medium text-gray-900">Advertiser URL:</dt>
                <dd class="text-sm/6 text-gray-700">
                    <a :href="item.requirements?.advertiser_url" target="_blank" class="flex flex-row gap-1 items-center text-blue-500 hover:text-blue-700">
                        <span>{{ item.requirements?.advertiser_url ?? 'N/A' }}</span>
                        <ExternalLink class="w-4 h-4" v-if="item.requirements?.advertiser_url" />
                    </a>
                </dd>
            </div>

            <div class="flex gap-x-3 border-b border-gray-100 items-center">
                <dt class="text-sm/6 font-medium text-gray-900">Anchor Keyword:</dt>
                <dd class="text-sm/6 text-gray-700">
                    {{ item.requirements?.anchor_text ?? 'N/A' }}
                </dd>
            </div>

            <div class="flex gap-x-3 border-b border-gray-100 items-center">
                <dt class="text-sm/6 font-medium text-gray-900">Topic:</dt>
                <dd class="text-sm/6 text-gray-700">
                    {{ item.requirements?.article_topic ?? 'N/A' }}
                </dd>
            </div>

            <div class="flex gap-x-3 border-b border-gray-100 items-center">
                <dt class="text-sm/6 font-medium text-gray-900">Comments:</dt>
                <dd class="text-sm/6 text-gray-700">
                    {{ item.requirements?.requirement_comments ?? 'N/A' }}
                </dd>
            </div>
        </div>

        <!-- Edit Mode -->
        <form @submit.prevent="updateRequirements" class="flex flex-col gap-3 bg-gray-50 border border-gray-200 rounded-lg p-6" v-show="showEdit">
            <div class="flex flex-row gap-2 items-center">
                <label for="article_topic" class="w-40 text-sm/6 font-medium text-gray-900 whitespace-nowrap">Article
                    Topic:</label>
                <input type="text" id="article_topic" class="w-full rounded-lg text-sm/6 text-gray-700" v-model="editableRequirements.article_topic" />
            </div>
            <div class="flex flex-row gap-2 items-center">
                <label for="anchor_text" class="w-40 text-sm/6 font-medium text-gray-900 whitespace-nowrap">Anchor Text
                </label>
                <input type="text" id="anchor_text" class="w-full rounded-lg text-sm/6 text-gray-700" v-model="editableRequirements.anchor_text" />
            </div>

            <div class="flex flex-row gap-2 items-center">
                <label for="advertiser_url" class="w-40 text-sm/6 font-medium text-gray-900 whitespace-nowrap">Advertiser URL:</label>
                <input type="text" id="advertiser_url" class="w-full rounded-lg text-sm/6 text-gray-700" v-model="editableRequirements.advertiser_url" />
            </div>
            <div class="flex flex-row gap-2 items-start">
                <label for="requirement_comments" class="w-40 text-sm/6 font-medium text-gray-900 whitespace-nowrap mt-2">Comments:</label>
                <textarea id="requirement_comments" class="w-full rounded-lg text-sm/6 text-gray-700" v-model="editableRequirements.requirement_comments"></textarea>
            </div>

            <div class="flex flex-row gap-2 justify-end mt-2">
                <button type="button" class="btn-gray" @click="cancelEdit" :disabled="isProcessing">Cancel</button>
                <button type="submit"
                        class="btn-indigo disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                        :disabled="isProcessing">
                    <svg v-if="isProcessing" class="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    {{ isProcessing ? 'Updating...' : 'Update Requirements' }}
                </button>
            </div>
        </form>
    </div>
</template>


<script setup>
import { ref, inject } from 'vue'
import { ExternalLink, Pencil, Check, CircleCheck } from 'lucide-vue-next'
import { router } from '@inertiajs/vue3'


const notify = inject('$notify')

const props = defineProps({
    item: Object,
    order: Object,
})

const emit = defineEmits(['requirements-updated'])

const showEdit = ref(false)

// Local reactive editable copy
const editableRequirements = ref({
    advertiser_url: '',
    anchor_text: '',
    article_topic: '',
    requirement_comments: '',
})

// On Edit Click: Copy current data
const startEdit = () => {
    showEdit.value = true
    editableRequirements.value = {
        advertiser_url: props.item.requirements?.advertiser_url ?? '',
        anchor_text: props.item.requirements?.anchor_text ?? '',
        article_topic: props.item.requirements?.article_topic ?? '',
        requirement_comments: props.item.requirements?.requirement_comments ?? '',
    }
}

// On Cancel
const cancelEdit = () => {
    showEdit.value = false
}

// Processing state for button protection
const isProcessing = ref(false)

// On Submit
const updateRequirements = () => {
    if (isProcessing.value) return; // Prevent multiple submissions

    isProcessing.value = true;

    // Update the original `item.requirements` object
    props.item.requirements = { ...editableRequirements.value }

    router.post(route("admin.orders.item.update-requirements", props.item.id), {
        requirements: editableRequirements.value
    }, {
        preserveScroll: true,
        onSuccess: () => {
            notify("Requirements updated successfully!", { type: "success" })
            emit('requirements-updated')
            showEdit.value = false
            isProcessing.value = false
        },
        onError: (errors) => {
            // Handle validation errors
            Object.entries(errors).forEach(([field, message]) => {
                notify(`${message}`, { type: "error" });
            });
            isProcessing.value = false
        },
        onFinish: () => {
            isProcessing.value = false
        }
    });
}
</script>


<style lang="scss" scoped></style>