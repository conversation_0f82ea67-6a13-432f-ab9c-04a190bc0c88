<template>
    <div class="lg:flex lg:items-center lg:justify-between">
        <div class="min-w-0 flex-1">
            <h2
                class="flex justify-start text-2xl text-gray-900 font-bold gap-1 items-center mt-2 sm:text-3xl sm:tracking-tight sm:truncate">
                <Link :href="route('publisher.orders.index', order.id)" class="link-indigo-icon">
                <CircleChevronLeft class="h-6 w-6" />
                </Link>
                <span>
                    Orders Item Details
                </span>
            </h2>
        </div>
    </div>
    <div class="flex flex-col gap-3">
        <div class="mt-5">
            <Header :order="order" :item="item"></Header>
        </div>

        <div class="flex flex-col md:flex-row gap-3">
            <div class=" w-full md:w-2/3  flex flex-col gap-3 ">
                <div>
                    <div class="border p-6 bg-white paper-shadow mb-5" v-if="item.state_name == orderItemStates.PublicationInProcess ||
                        item.state_name == orderItemStates.PublicationRevisionRequestedByAdvertiser">
                        <Publication :item="item" :revisions="revisions" />
                    </div>
                    <div class="border p-6 bg-white paper-shadow mb-5" v-if="item.content != null">
                        <Content :media="media" :content="item.content" />
                    </div>
                    <div class="border p-6 bg-white paper-shadow mb-5"
                        v-if="item.state_name != orderItemStates.RequirementsPending">
                        <Requirements :requirements="item.requirements" />
                    </div>
                </div>

                <div v-if="!shouldShowSecondDiv">
                    Order status is <span class="font-bold">{{ item.state_label }}</span>. Please wait for requirements
                    from advertiser.
                </div>
            </div>
            <div class=" w-full md:w-1/3  flex flex-col gap-3">
                <div class="bg-white border p-6 paper-shadow">
                    <!-- <VerticalTimeline></VerticalTimeline> -->
                    <VerticalTimeline :timeline="timeline" />
                </div>
                <div class="paper-shadow" v-if="page.props.auth?.user?.id">
                    <Chat :order="item"></Chat>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import Header from './Header.vue';
import Publication from './Publication.vue';
import Content from './Content.vue';
import Requirements from './Requirements.vue';
import VerticalTimeline from '../../../../Components/VerticalTimeline.vue';
import Chat from './Chat.vue';
import { CircleChevronLeft } from 'lucide-vue-next';
import { usePage } from '@inertiajs/vue3';
import { computed } from 'vue';


const page = usePage();
const orderItemStates = page.props.orderItemStates;

const props = defineProps({
    order: Object,
    item: Object,
    media: Array,
    revisions: String,
    timeline: Array
});

const shouldShowSecondDiv = computed(() => {
    return (
        props.item.state_name === orderItemStates.PublicationInProcess ||
        props.item.state_name === orderItemStates.PublicationRevisionRequestedByAdvertiser ||
        (props.item.content != null &&
            (props.item.content_status === 'Awaiting Publisher Approval' || props.item.content_status === 'Completed')) ||
        props.item.state_name !== orderItemStates.RequirementsPending
    );
});


</script>

<style lang="scss" scoped></style>