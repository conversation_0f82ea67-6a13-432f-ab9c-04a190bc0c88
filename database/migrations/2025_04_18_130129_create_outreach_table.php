<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('outreaches', function (Blueprint $table): void {
            $table->id();

            // Relations
            $table->foreignId('marketplace_website_id')->constrained()->cascadeOnDelete();
            $table->foreignId('user_id')->constrained()->cascadeOnDelete(); // outreach user

            // State Management
            $table->string('status', 50)->default('inprogress'); // ['inprogress', 'onboarded', 'rejected']
            $table->timestamp('onboarded_at')->nullable();
            $table->timestamp('rejected_at')->nullable();
            $table->text('notes')->nullable();

            // Optional tracking
            $table->timestamps();
        });

        // Enforce one assignment per website at a time
        Schema::table('outreaches', function (Blueprint $table): void {
            $table->unique('marketplace_website_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('outreaches');
    }
};
