<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_item_state_transitions', function (Blueprint $table): void {
            $table->id();
            $table->morphs('model'); // This will create model_id and model_type columns
            $table->string('from_state');
            $table->string('to_state');
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->json('details')->nullable(); // For storing additional details
            $table->timestamps();


            // $table->ipAddress('ip_address')->nullable(); //legacy, not useful, removed 11 jun
            // $table->string('user_agent')->nullable(); //legacy, not useful, removed 11 jun
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_item_state_transitions');
    }
};
