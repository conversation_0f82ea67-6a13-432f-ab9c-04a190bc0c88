<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

// Pivot table for keyword to website
// many to many connection
return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('keyword_website', function (Blueprint $table): void {
            // $table->id();
            $table->primary(['keyword_id', 'website_id']);
            $table->foreignId('keyword_id')
                ->constrained('serp_keywords')
                ->onDelete('cascade');
            $table->foreignId('website_id')
                ->constrained('marketplace_websites')
                ->onDelete('cascade');
            // $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::drop('keyword_website');
    }
};
