<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

// Pivot table for keyword to website
// many to many connection
return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('topic_website', function (Blueprint $table): void {

            $table->foreignId('topic_id')->constrained()->onDelete('cascade');
            $table->foreignId('marketplace_website_id')->constrained('marketplace_websites')->onDelete('cascade');
            $table->primary(['topic_id', 'marketplace_website_id']);

            // $table->id();
            // $table->primary(['topic_id', 'website_id']);
            // $table->foreignId('topic_id')->constrained('topics')->onDelete('cascade');
            // $table->foreignId('website_id')->constrained('marketplace_websites')->onDelete('cascade');
            // $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::drop('topic_website');
    }
};
