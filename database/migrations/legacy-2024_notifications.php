<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    // public function up(): void
    // {
    //     Schema::create('marketplace_notifications', function (Blueprint $table) {

    //         $table->id();
    //         $table->foreignId('user_id')->constrained()->onDelete('cascade');

    //         $table->string('notification_to', 100); //(seller, buyer, support)
    //         $table->string('notification_status', 100); //(pending, sent, scheduled)
    //         $table->string('notification_mode', 100);
    //         $table->string('notification_subject', 100); //(order delivered, order placed)
    //         $table->string('notification_content');

    //         $table->timestamps();

    //     });
    // }

    // /**
    //  * Reverse the migrations.
    //  */
    // public function down(): void
    // {
    //     Schema::dropIfExists('marketplace_notifications');
    // }
};
