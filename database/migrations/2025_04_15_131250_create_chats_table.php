<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chats', function (Blueprint $table): void {
            $table->id();
            $table->foreignId(column: 'order_item_id')->constrained('marketplace_single_order_items')->onDelete('cascade');
            $table->text('text')->nullable();
            $table->string('attachment_path')->nullable();
            $table->foreignId(column: 'sender_id')->constrained('users')->onDelete('cascade');
            $table->foreignId(column: 'receiver_id')->constrained('users')->onDelete('cascade');
            $table->boolean('is_read')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chats');
    }
};
