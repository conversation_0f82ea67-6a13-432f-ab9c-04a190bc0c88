<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_item_publications', function (Blueprint $table): void {
            $table->id();
            $table->foreignId('order_item_id');
            $table->foreignId('marketplace_website_id');
            $table->string('publication_url');
            $table->enum('link_type', ['dofollow', 'nofollow', 'sponsor']);
            $table->boolean('active')->default(true);
            $table->boolean('indexed')->default(false);
            $table->text('advertiser_revision_reason')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_item_publications');
    }
};
