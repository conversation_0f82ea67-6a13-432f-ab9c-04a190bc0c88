<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('marketplace_website_seo_stat_logs', function (Blueprint $table): void {
            $table->id();
            $table->foreignId('marketplace_website_id')->constrained('marketplace_websites');
            $table->string('source');
            $table->string('type');
            $table->json('data')->nullable();
            $table->integer('tries')->default(0);
            $table->boolean('is_success')->default(false);
            $table->json('error_message')->nullable();
            $table->dateTime('record_updated_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('marketplace_website_seo_stat_logs');
    }
};
