<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('media', function (Blueprint $table): void {
            $table->id();
            $table->foreignId(column: 'user_id')->constrained('users')->onDelete('cascade');
            $table->string('path');
            $table->string('purpose')->default('user_media')->index('purpose');
            $table->string('mime_type');
            $table->string('disk')->default('r2');
            $table->nullableMorphs('mediable'); // Make mediable fields nullable
            $table->json('meta')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('media');
    }
};
