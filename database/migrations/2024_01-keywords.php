<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('serp_keywords', function (Blueprint $table): void {
            $table->id();
            $table->string('name')->unique()->index();
            $table->unsignedBigInteger('volume')->nullable(); //search volume
            $table->decimal('cpc', 8, 2)->nullable(); // ads cpc (7 place, 2 precision)
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::drop('serp_keywords');
    }
};
