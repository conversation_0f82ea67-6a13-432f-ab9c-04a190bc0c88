<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('marketplace_orders', function (Blueprint $table): void {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('payment_id')->constrained('order_payments')->onDelete('cascade');
            $table->string('status')->default('pending');
            $table->decimal('price_paid', 10, 2)->default(0);
            $table->decimal('our_cost', 10, 2)->default(0); //total we credited to publishers
            $table->decimal('final_received_amount', 10, 2)->default(0); //final amount: after cancellation + refunds
            $table->unsignedInteger('items_in_orders')->default(1);
            $table->text('advertiser_note')->nullable();
            $table->text('internal_note')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('marketplace_orders');
    }
};
