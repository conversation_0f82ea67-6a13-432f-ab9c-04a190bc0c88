<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cart_items', function (Blueprint $table): void {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('marketplace_website_id')->constrained('marketplace_websites')->onDelete('cascade');
            $table->string('niche', 100); //defined here: App\Enums\Niche
            $table->boolean('content_writing')->nullable()->default(false);
            $table->timestamps();


            // $table->integer('quantity')->default(1); //legacy, removed 11 jun
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cart_items');
    }
};
