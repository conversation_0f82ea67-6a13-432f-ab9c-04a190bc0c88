<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_item_requirements', function (Blueprint $table): void {
            $table->id();
            $table->foreignId('order_item_id')->constrained('marketplace_single_order_items')->onDelete('cascade');
            $table->string('article_topic');
            $table->string('anchor_text');
            $table->string('advertiser_url');
            $table->text('requirement_comments')->nullable();
            $table->text('advertiser_revision_reason')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_item_requirements');
    }
};
