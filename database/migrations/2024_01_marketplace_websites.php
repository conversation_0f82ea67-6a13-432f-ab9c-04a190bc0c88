<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('marketplace_websites', function (Blueprint $table): void {

            $table->id();
            $table->string('website_domain', 300)->unique();
            // add column for turn around time in number of days
            // $table->unsignedTinyInteger('turn_around_time_in_days')->default(0);


            //Website Data
            $table->foreignId('site_language_id')->default(0)->index(); //bz
            $table->foreignId('main_category_id')->default(0)->index(); //similarweb
            $table->unsignedInteger('category_global_rank')->default(0); //similarweb (change to nullable v2)

            $table->string('site_title', 700)->nullable(); //similarweb
            $table->string('site_description', 1000)->nullable(); //similarweb
            $table->date('domain_registration_date')->nullable(); //bz

            $table->foreignId('site_icon_image_id')->default(0); //google
            $table->enum('publication_type', ['article', 'press-release', 'news'])->default('article');

            $table->unsignedTinyInteger('pb_seo_impact_score')->default(1);
            $table->unsignedTinyInteger('pb_brand_awareness_impact_score')->default(1);


            //Pricing

            $table->unsignedInteger('guest_post_price')->index();
            $table->unsignedInteger('link_insert_price')->default(0);
            $table->unsignedInteger('casino_post_price')->default(0)->index();
            $table->unsignedInteger('adult_post_price')->default(0)->index();
            $table->unsignedInteger('finance_post_price')->default(0)->index();
            $table->unsignedInteger('dating_post_price')->default(0)->index();
            $table->unsignedInteger('cbd_post_price')->default(0)->index();
            $table->unsignedInteger('crypto_post_price')->default(0)->index();



            //Publishing
            $table->text('site_requirements')->nullable();
            $table->string('example_post_url', 1000)->nullable();
            $table->unsignedSmallInteger('article_validity_in_months')->default(36);
            $table->unsignedTinyInteger('turn_around_time_in_days')->default(0);

            $table->boolean('indexed_article')->default(1);
            $table->string('link_relation', 100)->default('dofollow');
            $table->boolean('sponsorship_label')->default(false);
            $table->boolean('homepage_visible')->default(0);


            //Meta
            $table->foreignId('publisher_user_id')->default(0);


            // $table->boolean('verified')->default(0); //for pricing confirmation
            $table->string('contact_email', 500)->nullable();
            $table->boolean('active')->default(false);
            $table->string('site_source', 200)->default('self');
            $table->string('internal_note', 700)->nullable(); //added ourself from source or by user
            $table->fullText(['site_title', 'site_description'], 'title_descp_index_fulltext');
            // $table->date('similarweb_data_last_updated')->default(now());
            // $table->date('ahref_data_last_updated')->default(now());
            $table->timestamps();



            // ------------------------------------------
            // Removed
            // $table->string('site_type', 100)->nullable(); //blog/megazine/etc

        });
    }



    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('marketplace_websites');
    }
};
