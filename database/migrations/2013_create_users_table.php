<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table): void {

            $table->id();
            $table->string('name');
            $table->string('email')->unique();
            $table->string('role')->default('advertiser'); //available roles in App\Enums
            $table->string('phone')->nullable();


            //convert next 3 columns to single json column (city, address, postal_code) ‼️
            $table->json('address_data')->nullable();


            $table->foreignId('country_id')->nullable();
            $table->string('company', 100)->nullable();
            $table->boolean('active')->default(true); //so we can disable


            $table->string('password');
            $table->rememberToken();

            $table->ipAddress('last_login_ip')->nullable(); //remove if session already stores it ‼️
            $table->timestamp('email_verified_at')->nullable(); //Email verification completed
            $table->softDeletes(); // add soft delete
            $table->timestamps();


            // add nullable primary domain
            $table->string('primary_domain')->nullable()->unique();
        });
    }

    /** imran
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
    }
};
