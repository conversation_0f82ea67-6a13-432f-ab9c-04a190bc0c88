<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('marketplace_checkout_sessions', function (Blueprint $table): void {

            $table->id();
            $table->foreignId('user_id')
                ->constrained()
                ->onDelete('cascade');
            $table->json('cart_items_ids');
            $table->integer('total_amount')->default(0);
            $table->integer('total_items');
            $table->boolean('checkout_successful')->default(0);
            $table->string('stripe_payment_intent');
            $table->ipAddress('checkout_ip_address');
            $table->string('order_memo', 500)->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('marketplace_checkout_session');
    }
};
