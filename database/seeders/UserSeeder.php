<?php

namespace Database\Seeders;

use App\Enums\Role;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\App;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {



        $yesterday = Carbon::now()->subDay(); // Get yesterday's date

        User::create([
            'name' => 'Super Admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => Role::SuperAdmin->value,
            'email_verified_at' => $yesterday,
        ]);

        User::create([
            'name' => 'Admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => Role::Admin->value,
            'email_verified_at' => $yesterday,
        ]);


        if (App::environment(['local', 'staging'])) {


            User::create([
                'name' => 'Publisher',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'role' => Role::Publisher->value,
                'email_verified_at' => $yesterday,
            ]);

            User::create([
                'name' => 'Advertiser',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'role' => Role::Advertiser->value,
                'email_verified_at' => $yesterday,
            ]);


            User::create([
                'name' => 'Sales',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'role' => Role::Sales->value,
                'email_verified_at' => $yesterday,
            ]);


            User::create([
                'name' => 'Finance',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'role' => Role::Finance->value,
                'email_verified_at' => $yesterday,
            ]);

            User::create([
                'name' => 'Outreach',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'role' => Role::Outreach->value,
                'email_verified_at' => $yesterday,
            ]);


            User::create([
                'name' => 'Outreach2',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'role' => Role::Outreach->value,
                'email_verified_at' => $yesterday,
            ]);


            User::create([
                'name' => 'Writer1',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'role' => Role::Writer->value,
                'email_verified_at' => $yesterday,
            ]);

            User::create([
                'name' => 'Writer2',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'role' => Role::Writer->value,
                'email_verified_at' => $yesterday,
            ]);

            // Create 100 random advertisers
            User::factory()->count(10)->create([
                'role' => fn() => Arr::random([Role::Advertiser->value, Role::Publisher->value]),
            ]);
        }
    }
}
