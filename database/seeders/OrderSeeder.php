<?php

namespace Database\Seeders;

use App\Enums\Role;
use App\Models\MarketplaceOrder;
use App\Models\MarketplacePayment;
use App\Models\MarketplaceSingleOrderItem;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\App;

class OrderSeeder extends Seeder
{
    public function run(): void
    {
        if (! App::environment(['local', 'staging'])) {
            $this->command
                ->warn('OrderSeeder can only run in local or staging environments.');
            return;
        }

        $users = User::whereIn('role', [Role::Advertiser->value])->get();

        for ($i = 0; $i < 10; $i++) {
            $user = $users->random();

            // Step 1: Create payment
            $payment = MarketplacePayment::factory()->create([
                'user_id' => $user->id,
                'payment_amount' => 0,
            ]);

            $itemCount = rand(1, 5);
            $totalPricePaid = 0;
            $totalOurCost = 0;

            // We'll collect the order items first
            $orderItems = [];

            for ($j = 0; $j < $itemCount; $j++) {
                $itemCreatedAt = fake()->dateTimeBetween('-3 months', '-20 days');
                $itemUpdatedAt = Carbon::parse($itemCreatedAt)->addDays(fake()->numberBetween(1, 15));
                $itemPrice = fake()->numberBetween(50, 1000);

                $orderItems[] = [
                    'price_paid' => $itemPrice,
                    'created_at' => $itemCreatedAt,
                    'updated_at' => $itemUpdatedAt,
                ];

                $totalPricePaid += $itemPrice;
                $totalOurCost += $itemPrice / 1.6;
            }

            $orderUpdatedAt = fake()->dateTimeBetween('-15 days', 'now');

            // Step 2: Create order with final values
            $order = MarketplaceOrder::factory()->create([
                'user_id' => $user->id,
                'payment_id' => $payment->id,
                'price_paid' => $totalPricePaid,
                'our_cost' => round($totalOurCost, 2),
                'items_in_orders' => $itemCount,
                'updated_at' => $orderUpdatedAt,
            ]);

            // Step 3: Create items linked to order
            foreach ($orderItems as $itemData) {
                MarketplaceSingleOrderItem::factory()->create([
                    'order_id' => $order->id,
                ]);
            }

            // Step 4: Update payment with final amount
            $payment->update([
                'payment_amount' => $totalPricePaid,
            ]);
        }
    }
}
