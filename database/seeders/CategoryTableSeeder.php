<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CategoryTableSeeder extends Seeder
{
    public function run(): void
    {


        $categories = [
            'Arts & Entertainment' => [
                'Animation and Comics',
                'Books and Literature',
                'Humor',
                'Music',
                'Performing Arts',
                'Streaming & Online TV',
                'Visual Arts and Design',
            ],
            'Business and Consumer Services' => [
                'Business Services',
                'Marketing and Advertising',
                'Digital Marketing',
                'Printing & Self Publishing',
                'Real Estate',
                'Moving & Relocation',
                'Shipping and Logistics',
                'Textiles',
            ],
            'Community and Society' => [
                'Decease',
                'Faith and Beliefs',
                'Holidays and Seasonal Events',
                'LGBTQ',
                'Philanthropy',
                'Dating and Relationships',
            ],
            'Computers Electronics and Technology' => [
                'Advertising Networks',
                'Computer Hardware',
                'Computer Security',
                'Consumer Electronics',
                'Email',
                'File Sharing and Hosting',
                'Graphics Multimedia and Web Design',
                'Programming and Developer Software',
                'Search Engines',
                'Social Media Networks',
                'Telecommunications',
                'Web Hosting and Domain Names',
            ],
            'Ecommerce & Shopping' => [
                'Auctions',
                'Classifieds',
                'Coupons and Rebates',
                'Marketplace',
                'Price Comparison',
                'Tickets',
            ],
            'Finance' => [
                'Accounting and Auditing',
                'Banking Credit and Lending',
                'Financial Planning and Management',
                'Insurance',
                'Investing',
            ],
            'Food and Drink' => [
                'Beverages',
                'Cooking and Recipes',
                'Groceries',
                'Restaurants and Delivery',
                'Vegetarian and Vegan',
            ],
            'Gambling' => [
                'Bingo',
                'Casinos',
                'Lottery',
                'Poker',
                'Betting',
            ],
            'Games' => [
                'Board and Card Games',
                'Puzzles and Brainteasers',
                'Roleplaying Games',
                'Video Consoles and Accessories',
            ],
            'Health' => [
                'Addictions',
                'Alternative and Natural Medicine',
                'Biotechnology and Pharmaceuticals',
                'Childrens Health',
                'Dentist and Dental Services',
                'Developmental and Physical Disabilities',
                'Geriatric and Aging Care',
                'Conditions and Concerns',
                'Medicine',
                'Mens Health',
                'Mental Health',
                'Nutrition Diets and Fitness',
                'Pharmacy',
                'Public and Safety',
                'Womens Health',
            ],
            'Heavy Industry and Engineering' => [
                'Aerospace and Defense',
                'Agriculture',
                'Architecture',
                'Chemical Industry',
                'Construction and Maintenance',
                'Energy Industry',
                'Metals and Mining',
                'Waste Water and Environmental',
            ],
            'Hobbies and Leisure' => [
                'Ancestry and Genealogy',
                'Antiques and Collectibles',
                'Camping Scouting and Outdoors',
                'Crafts',
                'Models',
                'Photography',
            ],
            'Home and Garden' => [
                'Furniture',
                'Gardening',
                'Home Improvement and Maintenance',
                'Interior Design',
            ],
            'Jobs and Career' => [
                'Human Resources',
                'Jobs and Employment',
            ],
            'Law and Government' => [
                'Government',
                'Immigration and Visas',
                'Law Enforcement and Protective Services',
                'Legal',
                'National Security',
            ],
            'Lifestyle' => [
                'Beauty and Cosmetics',
                'Childcare',
                'Fashion and Apparel',
                'Gifts and Flowers',
                'Jewelry and Luxury Products',
                'Tobacco',
                'Weddings',
            ],
            'News & Media Publishers' => [],
            'Pets and Animals' => [
                'Animals',
                'Birds',
                'Fish and Aquaria',
                'Horses',
                'Pet Food and Supplies',
                'Pets',
            ],
            'Reference Materials' => [
                'Dictionaries and Encyclopedias',
                'Maps',
                'Public Records and Directories',
            ],
            'Science and Education' => [
                'Astronomy',
                'Biology',
                'Business Training',
                'Chemistry',
                'Earth Sciences',
                'Education',
                'Environmental Science',
                'Grants Scholarships and Financial Aid',
                'History',
                'Libraries and Museums',
                'Literature',
                'Math',
                'Philosophy',
                'Physics',
                'Public Records and Directories',
                'Social Sciences',
                'Universities and Colleges',
                'Weather',
            ],
            'Sports' => [
                'American Football',
                'Baseball',
                'Basketball',
                'Boxing',
                'Climbing',
                'Cycling and Biking',
                'Extreme Sports',
                'Fantasy Sports',
                'Fishing',
                'Golf',
                'Hunting and Shooting',
                'Martial Arts',
                'Rugby',
                'Running',
                'Soccer',
                'Tennis',
                'Volleyball',
                'Water Sports',
                'Winter Sports',
            ],
            'Travel and Tourism' => [
                'Accommodation and Hotels',
                'Air Travel',
                'Car Rentals',
                'Ground Transportation',
                'Tourist Attractions',
                'Transportation and Excursions',
            ],
            'Vehicles' => [
                'Automotive Industry',
                'Aviation',
                'Boats',
                'Makes and Models',
                'Motorcycles',
                'Motorsports',
            ],
            'Adult' => [],
            'Other' => [],
        ];

        foreach ($categories as $parentCategory => $subCategories) {
            $parentId = DB::table('website_categories')->insertGetId([
                'category' => $parentCategory,
                'parent' => 1,
                'parent_category_id' => 0,
            ]);

            foreach ($subCategories as $subCategory) {
                DB::table('website_categories')->insert([
                    'category' => $subCategory,
                    'parent' => 0,
                    'parent_category_id' => $parentId,
                ]);
            }
        }
    }
}
